-- Valic Housing System - Payment and Key Management
-- Author: stepan_valic

-- Key Management Functions
lib.callback.register('valic_housing:getHouseKeys', function(source, houseId)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end
    
    -- Get all keyholders
    local keys = MySQL.query.await([[
        SELECT vhk.*, p.charinfo
        FROM valic_housing_keys vhk
        LEFT JOIN players p ON p.citizenid = vhk.charid
        WHERE vhk.house_id = ? AND vhk.is_active = 1
    ]], {houseId})
    
    return true, 'success', keys
end)

lib.callback.register('valic_housing:giveKey', function(source, houseId, targetCharid)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end
    
    -- Check if target already has key
    local existingKey = MySQL.query.await('SELECT * FROM valic_housing_keys WHERE house_id = ? AND charid = ? AND is_active = 1', {
        houseId, targetCharid
    })
    
    if existingKey[1] then
        return false, 'already_has_key'
    end
    
    -- Give key
    MySQL.insert('INSERT INTO valic_housing_keys (house_id, charid, access_level, granted_by) VALUES (?, ?, ?, ?)', {
        houseId, targetCharid, 'keyholder', charid
    })
    
    -- Log key grant
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'key_grant', json.encode({target = targetCharid})
    })
    
    -- Discord log
    SendDiscordLog('🔑 Key Granted', string.format('Key granted for house %d', houseId), 65280, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Granted By', value = charid, inline = true},
        {name = 'Granted To', value = targetCharid, inline = true}
    })
    
    -- Notify target player if online
    local targetPlayer = GetPlayerByCitizenId(targetCharid)
    if targetPlayer then
        TriggerClientEvent('valic_housing:keyReceived', targetPlayer.PlayerData.source, houseId)
    end
    
    return true, 'key_given'
end)

lib.callback.register('valic_housing:revokeKey', function(source, houseId, targetCharid)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end
    
    -- Cannot revoke owner's access
    if targetCharid == charid then
        return false, 'cannot_revoke_owner'
    end
    
    -- Revoke key
    local affected = MySQL.update.await('UPDATE valic_housing_keys SET is_active = 0 WHERE house_id = ? AND charid = ? AND is_active = 1', {
        houseId, targetCharid
    })

    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 Revoked key for house %d, charid %s, affected rows: %d', houseId, targetCharid, affected))
    end

    if affected == 0 then
        return false, 'no_key'
    end
    
    -- Log key revoke
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'key_revoke', json.encode({target = targetCharid})
    })
    
    -- Discord log
    SendDiscordLog('🔑 Key Revoked', string.format('Key revoked for house %d', houseId), 16711680, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Revoked By', value = charid, inline = true},
        {name = 'Revoked From', value = targetCharid, inline = true}
    })
    
    -- Notify target player if online
    local targetPlayer = GetPlayerByCitizenId(targetCharid)
    if targetPlayer then
        TriggerClientEvent('valic_housing:keyRevoked', targetPlayer.PlayerData.source, houseId)
    end
    
    return true, 'key_revoked'
end)

-- Sell Property
lib.callback.register('valic_housing:sellProperty', function(source, houseId)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end
    
    -- Calculate sale price
    local salePrice = math.floor(houseData.purchase_price * ServerConfig.Payment.resalePercentage)
    
    -- Add money to player
    if not AddPlayerMoney(player, 'bank', salePrice) then
        return false, 'payment_failed'
    end
    
    -- Remove house ownership
    MySQL.update('UPDATE valic_housing SET is_active = 0 WHERE house_id = ?', {houseId})
    
    -- Remove all keys
    MySQL.update('UPDATE valic_housing_keys SET is_active = 0 WHERE house_id = ?', {houseId})
    
    -- Generate random password for doors (house is now unowned)
    local randomPassword = GenerateRandomCode(8)
    local house = Config.Houses[houseId]
    if house and house.doorIds then
        UpdateDoorPasswords(house.doorIds, randomPassword)

        if Config.Debug then
            print(string.format('^3[Valic Housing]^7 House %d sold, doors secured with random password', houseId))
        end
    end
    
    -- Log sale
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'sale', json.encode({sale_price = salePrice, original_price = houseData.purchase_price})
    })
    
    -- Discord log
    SendDiscordLog('🏠 Property Sold', string.format('House %d sold by %s', houseId, charid), 16776960, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Sale Price', value = ServerConfig.Payment.currencySymbol .. salePrice, inline = true},
        {name = 'Original Price', value = ServerConfig.Payment.currencySymbol .. houseData.purchase_price, inline = true}
    })

    -- Inform all clients about property sale
    TriggerClientEvent('valic_housing:propertySold', -1, houseId)

    return true, 'property_sold', {salePrice = salePrice}
end)

-- Get nearby players callback moved to main.lua due to framework scope

-- Get player's house information
lib.callback.register('valic_housing:getPlayerHouseInfo', function(source, houseId)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check access
    if not HasHouseAccess(charid, houseId) then
        return false, 'no_access'
    end
    
    local houseData = GetHouseData(houseId)
    if not houseData then return false, 'invalid_house' end
    
    local house = Config.Houses[houseId]
    if not house then return false, 'invalid_house' end
    
    return true, 'success', {
        id = houseId,
        name = house.name,
        price = house.price,
        purchase_price = houseData.purchase_price,
        purchase_date = houseData.purchase_date,
        owner_charid = houseData.owner_charid,
        is_owner = houseData.owner_charid == charid,
        resale_value = math.floor(houseData.purchase_price * ServerConfig.Payment.resalePercentage)
    }
end)
