-- Valic Housing System - NPC Management
-- Author: stepan_valic

local spawnedNPCs = {}
local spawnedBlips = {}

-- Create NPC for house
local function CreateHouseNPC(house)
    local model = Config.NPCModel
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end
    
    local npc = CreatePed(4, model, house.npc.coords.x, house.npc.coords.y, house.npc.coords.z - 1.0, house.npc.coords.w, false, true)
    
    SetEntityHeading(npc, house.npc.coords.w)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)
    
    if house.npc.scenario then
        TaskStartScenarioInPlace(npc, house.npc.scenario, 0, true)
    end
    
    -- Add ox_target interaction
    exports.ox_target:addLocalEntity(npc, {
        {
            name = 'valic_housing_npc_' .. house.id,
            icon = 'fas fa-home',
            label = exports.valic_housing:L('npc.interaction') or 'Mluvit s realitním ma<PERSON>',
            onSelect = function()
                OpenHouseMenu(house.id)
            end,
            distance = 2.0
        }
    })
    
    spawnedNPCs[house.id] = npc
    
    SetModelAsNoLongerNeeded(model)
end

-- Create Security NPC for owned house (no target interaction)
local function CreateSecurityNPC(house)
    local model = Config.SecurityModel

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end

    local npc = CreatePed(4, model, house.npc.coords.x, house.npc.coords.y, house.npc.coords.z - 1.0, house.npc.coords.w, false, true)

    SetEntityHeading(npc, house.npc.coords.w)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)

    -- Nastavení aby NPC nemohl být zraněn nebo zabit
    SetEntityCanBeDamaged(npc, false)
    SetEntityProofs(npc, true, true, true, true, true, true, true, true)
    SetPedCanRagdoll(npc, false)
    SetPedCanBeTargetted(npc, false)
    SetPedCanBeTargettedByPlayer(npc, PlayerId(), false)

    -- Dej security guard zbraň z configu
    local weaponHash = GetHashKey(Config.SecurityWeapon)
    GiveWeaponToPed(npc, weaponHash, 999, false, true)
    SetPedWeaponTintIndex(npc, weaponHash, 0) -- Základní barva
    SetCurrentPedWeapon(npc, weaponHash, true)

    -- Nastav aby NPC neútočil a byl pasivní
    SetPedCombatAttributes(npc, 17, true) -- BF_CanFightArmedPedsWhenNotArmed
    SetPedCombatAttributes(npc, 5, true)  -- BF_AlwaysFight
    SetPedCombatAttributes(npc, 46, true) -- BF_AlwaysEquipBestWeapon
    SetPedFleeAttributes(npc, 0, false)   -- Nebude utíkat
    SetPedCombatRange(npc, 0)             -- Nebude bojovat
    SetPedSeeingRange(npc, 0.0)           -- Nebude vidět nepřátele
    SetPedHearingRange(npc, 0.0)          -- Nebude slyšet
    SetPedAlertness(npc, 0)               -- Nebude ve střehu
    SetPedCombatMovement(npc, 0)          -- Nebude se pohybovat v boji

    -- Security guard scenario (standing at attention)
    -- Zkusíme nejdříve army scenario, pokud nefunguje, použijeme manuální animaci
    if not TaskStartScenarioInPlace(npc, Config.SecurityScenario, 0, true) then
        -- Fallback: manuální animace pozoru
        RequestAnimDict('anim@amb@casino@hangout@ped_male@stand@02b@base')
        while not HasAnimDictLoaded('anim@amb@casino@hangout@ped_male@stand@02b@base') do
            Wait(1)
        end
        TaskPlayAnim(npc, 'anim@amb@casino@hangout@ped_male@stand@02b@base', 'base', 8.0, -8.0, -1, 1, 0, false, false, false)
    end

    -- No ox_target interaction for security guard

    spawnedNPCs[house.id] = npc

    SetModelAsNoLongerNeeded(model)

    if Config.Debug then
        print(string.format('^2[Valic Housing]^7 Created armed security guard for house %d', house.id))
    end
end

-- Create blip for house
local function CreateHouseBlip(house, status)
    local blip = AddBlipForCoord(house.npc.coords.x, house.npc.coords.y, house.npc.coords.z)
    
    local blipConfig = Config.Blips.AvailableProperty
    if status == 'owned' then
        blipConfig = Config.Blips.OwnedProperty
    elseif status == 'shared' then
        blipConfig = Config.Blips.SharedProperty
    end
    
    SetBlipSprite(blip, blipConfig.Sprite)
    SetBlipColour(blip, blipConfig.Color)
    SetBlipScale(blip, blipConfig.Scale)
    SetBlipDisplay(blip, blipConfig.Display)
    SetBlipAsShortRange(blip, blipConfig.ShortRange)
    
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(house.name .. ' - ' .. exports.valic_housing:L('property.' .. status))
    EndTextCommandSetBlipName(blip)
    
    spawnedBlips[house.id] = blip
end

-- Open house menu
function OpenHouseMenu(houseId)
    local house = Config.Houses[houseId]
    if not house then return end

    local menuOptions = {}

    -- Check if player has any active tour
    local currentTour = exports.valic_housing:GetCurrentTour()
    if currentTour then
        local tourHouse = Config.Houses[currentTour.houseId]
        local tourHouseName = tourHouse and tourHouse.name or 'Neznámá nemovitost'

        -- Show end tour option for any active tour
        table.insert(menuOptions, {
            title = exports.valic_housing:L('tour.end_active_tour'),
            description = string.format('Ukončit prohlídku: %s', tourHouseName),
            icon = 'fas fa-times-circle',
            onSelect = function()
                local alert = lib.alertDialog({
                    header = exports.valic_housing:L('tour.end_tour'),
                    content = exports.valic_housing:L('tour.end_tour_confirm', tourHouseName),
                    centered = true,
                    cancel = true,
                    labels = {
                        confirm = exports.valic_housing:L('tour.end_tour'),
                        cancel = exports.valic_housing:L('general.cancel')
                    }
                })

                if alert == 'confirm' then
                    local tourHouseId = currentTour.houseId

                    -- Clear local state immediately
                    TriggerEvent('valic_housing:clearTourState')

                    -- Inform server
                    TriggerServerEvent('valic_housing:endTour', tourHouseId)

                    lib.notify({
                        title = exports.valic_housing:L('tour.title'),
                        description = exports.valic_housing:L('tour.tour_ended_manual'),
                        type = 'success'
                    })
                end
            end
        })

        -- Add separator
        table.insert(menuOptions, {
            title = '────────────────',
            disabled = true
        })
    end

    -- Check if player has active tour for this specific house
    if currentTour and currentTour.houseId == houseId then
        -- Don't show normal options if this house is being toured
        lib.registerContext({
            id = 'valic_housing_main_menu',
            title = house.name .. ' - Aktivní prohlídka',
            options = menuOptions
        })

        return lib.showContext('valic_housing_main_menu')
    end

    -- Check if player has access to this house
    lib.callback('valic_housing:getPlayerHouseInfo', false, function(success, message, houseInfo)
        if success then
            -- Player has access - show management options
            table.insert(menuOptions, {
                title = exports.valic_housing:L('npc.manage_property'),
                description = exports.valic_housing:L('property.owner', houseInfo.owner_charid),
                icon = 'fas fa-cog',
                onSelect = function()
                    OpenManagementMenu(houseId, houseInfo)
                end
            })
        else
            -- House is available for purchase/tour
            table.insert(menuOptions, {
                title = exports.valic_housing:L('npc.view_property'),
                description = exports.valic_housing:L('tour.description'),
                icon = 'fas fa-eye',
                onSelect = function()
                    exports.valic_housing:StartTour(houseId)
                end
            })

            table.insert(menuOptions, {
                title = exports.valic_housing:L('npc.buy_property'),
                description = exports.valic_housing:L('property.price', house.price),
                icon = 'fas fa-shopping-cart',
                onSelect = function()
                    OpenPurchaseMenu(houseId, house)
                end
            })
        end

        lib.registerContext({
            id = 'valic_housing_main_menu',
            title = house.name,
            options = menuOptions
        })

        lib.showContext('valic_housing_main_menu')
    end, houseId)
end

-- Open purchase menu
function OpenPurchaseMenu(houseId, house)
    local alert = lib.alertDialog({
        header = exports.valic_housing:L('purchase.title'),
        content = exports.valic_housing:L('purchase.description', house.price),
        centered = true,
        cancel = true,
        labels = {
            confirm = exports.valic_housing:L('purchase.confirm'),
            cancel = exports.valic_housing:L('general.cancel')
        }
    })
    
    if alert == 'confirm' then
        -- Show payment method selection
        local paymentMethod = lib.inputDialog(exports.valic_housing:L('purchase.payment_method'), {
            {
                type = 'select',
                label = exports.valic_housing:L('purchase.payment_method'),
                options = {
                    {value = 'cash', label = exports.valic_housing:L('purchase.cash')},
                    {value = 'bank', label = exports.valic_housing:L('purchase.bank')}
                },
                required = true
            }
        })
        
        if paymentMethod then
            -- Get password
            local passwordInput = lib.inputDialog(exports.valic_housing:L('purchase.set_password'), {
                {
                    type = 'input',
                    label = exports.valic_housing:L('purchase.set_password'),
                    description = string.format('%d-%d %s', Config.MinPasswordLength, Config.MaxPasswordLength, 'číslice'),
                    required = true,
                    min = Config.MinPasswordLength,
                    max = Config.MaxPasswordLength
                }
            })
            
            if passwordInput and passwordInput[1] then
                exports.valic_housing:PurchaseProperty(houseId, paymentMethod[1], passwordInput[1])
            end
        end
    end
end

-- Function to update house status (change NPC type based on ownership)
local function UpdateHouseStatus(houseId)
    local house = Config.Houses[houseId]
    if not house then return end

    -- Check GLOBAL house ownership status (not player-specific)
    lib.callback('valic_housing:getGlobalHouseStatus', false, function(success, status, houseInfo)
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 UpdateHouseStatus for house %d: success=%s, status=%s', houseId, tostring(success), tostring(status)))
        end

        if success and status == 'owned' then
            -- House is owned - change to security guard and remove target
            if spawnedNPCs[houseId] then
                -- Remove old target
                exports.ox_target:removeLocalEntity(spawnedNPCs[houseId])

                -- Delete old NPC
                DeleteEntity(spawnedNPCs[houseId])
                spawnedNPCs[houseId] = nil
            end

            -- Create security guard NPC
            CreateSecurityNPC(house)

            -- Update blip to owned
            if spawnedBlips[houseId] then
                RemoveBlip(spawnedBlips[houseId])
                CreateHouseBlip(house, 'owned')
            end

            if Config.Debug then
                print(string.format('^2[Valic Housing]^7 Changed to security guard for owned house %d', houseId))
            end
        else
            -- House is available - show realtor NPC
            if spawnedNPCs[houseId] then
                -- Remove old NPC if it's security guard
                exports.ox_target:removeLocalEntity(spawnedNPCs[houseId])
                DeleteEntity(spawnedNPCs[houseId])
                spawnedNPCs[houseId] = nil
            end

            -- Create realtor NPC
            CreateHouseNPC(house)

            -- Update blip to available
            if spawnedBlips[houseId] then
                RemoveBlip(spawnedBlips[houseId])
                CreateHouseBlip(house, 'available')
            end

            if Config.Debug then
                print(string.format('^2[Valic Housing]^7 Changed to realtor NPC for available house %d', houseId))
            end
        end
    end, houseId)
end

-- Initialize NPCs and blips
CreateThread(function()
    Wait(3000) -- Wait for other resources and locales to load

    for _, house in pairs(Config.Houses) do
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Initializing house %d (%s)', house.id, house.name))
        end
        UpdateHouseStatus(house.id) -- Use update function instead of direct creation
    end

    if Config.Debug then
        print('^2[Valic Housing]^7 NPCs and blips initialized')
    end
end)

-- Update house status when property is purchased
RegisterNetEvent('valic_housing:propertyPurchased', function(houseId)
    UpdateHouseStatus(houseId)
end)

-- Update house status when property is sold
RegisterNetEvent('valic_housing:propertySold', function(houseId)
    UpdateHouseStatus(houseId)
end)

-- Universal house status update event
RegisterNetEvent('valic_housing:updateHouseStatus', function(houseId)
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 Received updateHouseStatus event for house %d', houseId))
    end
    UpdateHouseStatus(houseId)
end)

-- Admin panel targets (no NPC, just target zones)
CreateThread(function()
    Wait(2000) -- Wait for other resources to load

    for _, house in pairs(Config.Houses) do
        if house.adminPanel then
            -- Create invisible target zone for house management
            exports.ox_target:addBoxZone({
                coords = house.adminPanel.coords,
                size = vec3(2.0, 2.0, 2.0),
                rotation = 0,
                debug = house.tourZone and house.tourZone.debugMode or false,
                options = {
                    {
                        name = 'valic_housing_management_' .. house.id,
                        icon = 'fas fa-home',
                        label = 'Správa nemovitosti',
                        canInteract = function()
                            -- Check if player has access to this house
                            return true -- Always show, we'll check access in the function
                        end,
                        onSelect = function()
                            OpenHouseManagementPanel(house.id)
                        end,
                        distance = 2.0
                    }
                }
            })

            if Config.Debug then
                print(string.format('^3[Valic Housing]^7 Created management target zone for house %d at %s', house.id, house.adminPanel.coords))
            end
        end
    end
end)

-- Open house management panel (for admin panel target)
function OpenHouseManagementPanel(houseId)
    local house = Config.Houses[houseId]
    if not house then return end

    -- Check if player has access to this house
    lib.callback('valic_housing:getPlayerHouseInfo', false, function(success, message, houseInfo)
        if success then
            -- Player has access - show management options
            OpenManagementMenu(houseId, houseInfo)
        else
            lib.notify({
                title = exports.valic_housing:L('general.error') or 'Chyba',
                description = 'Nemáte přístup k této nemovitosti',
                type = 'error'
            })
        end
    end, houseId)
end

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        for _, npc in pairs(spawnedNPCs) do
            if DoesEntityExist(npc) then
                DeleteEntity(npc)
            end
        end
        
        for _, blip in pairs(spawnedBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
    end
end)
