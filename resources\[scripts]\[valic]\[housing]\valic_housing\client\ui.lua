-- Valic Housing System - UI Management
-- Author: stepan_valic

-- Management Menu
function OpenManagementMenu(houseId, houseInfo)
    local menuOptions = {
        {
            title = exports.valic_housing:L('management.property_info'),
            description = string.format('Cena: %s | Prodejní cena: %s',
                houseInfo.purchase_price,
                houseInfo.resale_value
            ),
            icon = 'fas fa-info-circle',
            disabled = true
        },
        {
            title = exports.valic_housing:L('management.change_password'),
            description = exports.valic_housing:L('password.change_title'),
            icon = 'fas fa-key',
            onSelect = function()
                OpenPasswordChangeMenu(houseId, houseInfo.is_owner)
            end
        }
    }
    
    if houseInfo.is_owner then
        table.insert(menuOptions, {
            title = exports.valic_housing:L('management.manage_keys'),
            description = exports.valic_housing:L('keys.title'),
            icon = 'fas fa-users',
            onSelect = function()
                OpenKeyManagementMenu(houseId)
            end
        })
        
        table.insert(menuOptions, {
            title = exports.valic_housing:L('management.sell_property'),
            description = string.format('Prodat za %s (30%% původní ceny)', houseInfo.resale_value),
            icon = 'fas fa-dollar-sign',
            onSelect = function()
                OpenSellPropertyMenu(houseId, houseInfo.resale_value)
            end
        })
    end
    
    lib.registerContext({
        id = 'valic_housing_management',
        title = exports.valic_housing:L('management.title'),
        menu = 'valic_housing_main_menu',
        options = menuOptions
    })
    
    lib.showContext('valic_housing_management')
end

-- Password Change Menu
function OpenPasswordChangeMenu(houseId, isOwner)
    local inputs = {}
    
    if not isOwner then
        table.insert(inputs, {
            type = 'input',
            label = exports.valic_housing:L('password.current_password'),
            password = true,
            required = true,
            min = Config.MinPasswordLength,
            max = Config.MaxPasswordLength
        })
    end
    
    table.insert(inputs, {
        type = 'input',
        label = exports.valic_housing:L('password.new_password'),
        description = string.format('%d-%d %s', Config.MinPasswordLength, Config.MaxPasswordLength, 'číslice'),
        password = true,
        required = true,
        min = Config.MinPasswordLength,
        max = Config.MaxPasswordLength
    })
    
    table.insert(inputs, {
        type = 'input',
        label = exports.valic_housing:L('password.confirm_password'),
        password = true,
        required = true,
        min = Config.MinPasswordLength,
        max = Config.MaxPasswordLength
    })
    
    local input = lib.inputDialog(exports.valic_housing:L('password.change_title'), inputs)
    
    if input then
        local currentPassword = isOwner and '' or input[1]
        local newPassword = isOwner and input[1] or input[2]
        local confirmPassword = isOwner and input[2] or input[3]
        
        if newPassword ~= confirmPassword then
            lib.notify({
                title = exports.valic_housing:L('general.error'),
                description = exports.valic_housing:L('password.mismatch'),
                type = 'error'
            })
            return
        end
        
        if not string.match(newPassword, '^%d+$') then
            lib.notify({
                title = exports.valic_housing:L('general.error'),
                description = exports.valic_housing:L('password.not_numeric'),
                type = 'error'
            })
            return
        end
        
        lib.callback('valic_housing:changePassword', false, function(success, message, data)
            if success then
                lib.notify({
                    title = exports.valic_housing:L('general.success'),
                    description = exports.valic_housing:L('password.changed'),
                    type = 'success'
                })
            else
                local errorMsg = exports.valic_housing:L('errors.' .. message)
                if message == 'password_cooldown' and data then
                    errorMsg = exports.valic_housing:L('password.cooldown', data.minutes)
                end
                
                lib.notify({
                    title = exports.valic_housing:L('general.error'),
                    description = errorMsg,
                    type = 'error'
                })
            end
        end, houseId, currentPassword, newPassword)
    end
end

-- Key Management Menu
function OpenKeyManagementMenu(houseId)
    lib.callback('valic_housing:getHouseKeys', false, function(success, message, keys)
        if not success then
            lib.notify({
                title = exports.valic_housing:L('general.error'),
                description = exports.valic_housing:L('errors.' .. message),
                type = 'error'
            })
            return
        end
        
        local menuOptions = {
            {
                title = exports.valic_housing:L('keys.give_key'),
                description = exports.valic_housing:L('keys.select_player'),
                icon = 'fas fa-plus',
                onSelect = function()
                    OpenGiveKeyMenu(houseId)
                end
            }
        }
        
        for _, key in pairs(keys) do
            local playerName = 'Unknown Player'
            if key.charinfo then
                local charinfo = json.decode(key.charinfo)
                playerName = charinfo.firstname .. ' ' .. charinfo.lastname
            end
            
            table.insert(menuOptions, {
                title = playerName,
                description = exports.valic_housing:L('keys.access_level') .. ': ' .. exports.valic_housing:L('keys.' .. key.access_level),
                icon = 'fas fa-user',
                onSelect = function()
                    OpenRevokeKeyMenu(houseId, key.charid, playerName)
                end
            })
        end
        
        lib.registerContext({
            id = 'valic_housing_keys',
            title = exports.valic_housing:L('keys.title'),
            menu = 'valic_housing_management',
            options = menuOptions
        })
        
        lib.showContext('valic_housing_keys')
    end, houseId)
end

-- Give Key Menu
function OpenGiveKeyMenu(houseId)
    print('^3[Valic Housing Debug]^7 OpenGiveKeyMenu called for house: ' .. houseId)
    print('^3[Valic Housing Debug]^7 Player coords: ' .. tostring(GetEntityCoords(PlayerPedId())))

    print('^3[Valic Housing Debug]^7 Calling lib.callback for getNearbyPlayers...')
    lib.callback('valic_housing:getNearbyPlayers', false, function(nearbyPlayers)
        print('^3[Valic Housing Debug]^7 Callback returned')
        print('^3[Valic Housing Debug]^7 nearbyPlayers type: ' .. type(nearbyPlayers))
        print('^3[Valic Housing Debug]^7 nearbyPlayers count: ' .. (nearbyPlayers and #nearbyPlayers or 'nil'))

        if nearbyPlayers then
            for i, player in pairs(nearbyPlayers) do
                print(string.format('^2[Valic Housing Debug]^7 Player %d: %s (%s) - Source: %d', i, player.name, player.characterName, player.source))
            end
        end

        if not nearbyPlayers or #nearbyPlayers == 0 then
            print('^1[Valic Housing Debug]^7 No nearby players found - showing error notification')
            lib.notify({
                title = exports.valic_housing:L('general.error'),
                description = 'Žádní hráči v okolí (10m)',
                type = 'error'
            })
            return
        end

        -- Show arrows above nearby players
        local arrows = {}
        for _, player in pairs(nearbyPlayers) do
            local arrow = lib.zones.sphere({
                coords = player.coords,
                radius = 1.0,
                debug = false,
                inside = function()
                    -- Draw arrow above player (lower height)
                    DrawMarker(2, player.coords.x, player.coords.y, player.coords.z + 1.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3, 0.3, 0.3, 0, 255, 0, 200, false, true, 2, false, nil, nil, false)
                end
            })
            table.insert(arrows, arrow)
        end

        -- Create menu options
        local menuOptions = {}
        for _, player in pairs(nearbyPlayers) do
            table.insert(menuOptions, {
                label = player.name,
                description = string.format('Postava: %s | ID: %d', player.characterName, player.source),
                icon = 'user',
                args = {
                    playerData = player,
                    arrows = arrows
                }
            })
        end

        -- Add close option
        table.insert(menuOptions, {
            label = 'Zrušit',
            description = 'Zrušit výběr hráče',
            icon = 'times',
            args = {
                isCancel = true,
                arrows = arrows
            }
        })

        -- Register and show menu
        lib.registerMenu({
            id = 'valic_housing_give_key',
            title = exports.valic_housing:L('keys.give_key'),
            position = 'top-right',
            options = menuOptions,
            onClose = function()
                -- Remove arrows when menu is closed (ESC/Backspace)
                for _, arrow in pairs(arrows) do
                    arrow:remove()
                end
                print('^3[Valic Housing Debug]^7 Menu closed, arrows removed')
            end
        }, function(selected, scrollIndex, args)
            print('^3[Valic Housing Debug]^7 Menu selection: ' .. selected .. ', args: ' .. json.encode(args))

            -- Remove arrows
            if args.arrows then
                for _, arrow in pairs(args.arrows) do
                    arrow:remove()
                end
            end

            -- Handle selection
            if args.isCancel then
                -- Just close menu (arrows already removed)
                return
            end

            if args.playerData then
                -- Give key to selected player
                lib.callback('valic_housing:giveKey', false, function(success, message)
                    if success then
                        lib.notify({
                            title = exports.valic_housing:L('general.success'),
                            description = exports.valic_housing:L('keys.key_given', args.playerData.name),
                            type = 'success'
                        })
                    else
                        lib.notify({
                            title = exports.valic_housing:L('general.error'),
                            description = exports.valic_housing:L('errors.' .. message),
                            type = 'error'
                        })
                    end
                end, houseId, args.playerData.citizenid)
            end
        end)

        lib.showMenu('valic_housing_give_key')
    end, houseId)
end

-- Revoke Key Menu
function OpenRevokeKeyMenu(houseId, targetCharid, playerName)
    local alert = lib.alertDialog({
        header = exports.valic_housing:L('keys.revoke_key'),
        content = string.format('Odebrat klíče hráči %s?', playerName),
        centered = true,
        cancel = true,
        labels = {
            confirm = exports.valic_housing:L('general.confirm'),
            cancel = exports.valic_housing:L('general.cancel')
        }
    })
    
    if alert == 'confirm' then
        lib.callback('valic_housing:revokeKey', false, function(success, message)
            if success then
                lib.notify({
                    title = exports.valic_housing:L('general.success'),
                    description = exports.valic_housing:L('keys.key_revoked', playerName),
                    type = 'success'
                })
            else
                lib.notify({
                    title = exports.valic_housing:L('general.error'),
                    description = exports.valic_housing:L('errors.' .. message),
                    type = 'error'
                })
            end
        end, houseId, targetCharid)
    end
end

-- Sell Property Menu
function OpenSellPropertyMenu(houseId, salePrice)
    -- Show information table about creating a ticket for house sale
    local alert = lib.alertDialog({
        header = 'Prodej nemovitosti',
        content = string.format([[
Pro prodej nemovitosti je nutné založit tiket.

Informace o nemovitosti:
• Prodejní cena: %s (30%% původní ceny)
• ID nemovitosti: %d

Prosím založte tiket s žádostí o prodej domu.
Admin vám nemovitost prodá a převede peníze.
        ]], salePrice, houseId),
        centered = true,
        cancel = false,
        labels = {
            confirm = 'Rozumím'
        }
    })
end

-- Tour UI
local tourUI = {
    visible = false,
    data = nil
}

RegisterNetEvent('valic_housing:showTourUI', function(data)
    tourUI.visible = true
    tourUI.data = data
end)

RegisterNetEvent('valic_housing:hideTourUI', function()
    tourUI.visible = false
    tourUI.data = nil
end)

RegisterNetEvent('valic_housing:updateTourTimer', function(remaining)
    if tourUI.visible then
        tourUI.data.remaining = remaining
    end
end)

-- Tour UI Display (Bottom Right Corner)
CreateThread(function()
    while true do
        Wait(0)

        if tourUI.visible and tourUI.data then
            local remaining = tourUI.data.remaining or tourUI.data.expires

            if remaining > 0 then
                -- Calculate position for bottom right corner
                local textX = 0.85 -- Right side
                local textY = 0.85 -- Bottom

                -- Draw tour info in bottom right corner (no background)
                SetTextFont(4)
                SetTextScale(0.4, 0.4)
                SetTextColour(255, 255, 255, 255) -- White text
                SetTextOutline()
                SetTextEntry("STRING")

                -- Simple white text format
                local displayText = string.format(
                    'Kód k prohlídce %s\nZbývající čas prohlídky: %d min',
                    tourUI.data.code,
                    remaining
                )

                AddTextComponentString(displayText)
                DrawText(textX, textY)
            else
                tourUI.visible = false
                tourUI.data = nil
            end
        end
    end
end)

-- Admin Menu
function OpenAdminMenu(houseId)
    local menuOptions = {
        {
            title = exports.valic_housing:L('admin.reset_property'),
            description = 'Resetovat vlastnictví nemovitosti',
            icon = 'fas fa-undo',
            onSelect = function()
                -- Add admin reset functionality
                lib.notify({
                    title = exports.valic_housing:L('general.info'),
                    description = 'Admin funkce bude implementována',
                    type = 'info'
                })
            end
        },
        {
            title = exports.valic_housing:L('admin.view_logs'),
            description = 'Zobrazit logy nemovitosti',
            icon = 'fas fa-list',
            onSelect = function()
                -- Add admin logs functionality
                lib.notify({
                    title = exports.valic_housing:L('general.info'),
                    description = 'Admin funkce bude implementována',
                    type = 'info'
                })
            end
        }
    }
    
    lib.registerContext({
        id = 'valic_housing_admin',
        title = exports.valic_housing:L('admin.title'),
        options = menuOptions
    })
    
    lib.showContext('valic_housing_admin')
end
