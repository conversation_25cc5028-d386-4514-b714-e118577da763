-- Server-side configuration for Valic Housing System
-- This file contains sensitive information and should not be accessible to clients

ServerConfig = {}

-- Discord Webhook Configuration
ServerConfig.Discord = {
    webhook = "https://discord.com/api/webhooks/1402746389624262729/IRuL_3azV7O3eP8Vf_8Rrnup5LqGmcj1mz5MsXbOuONIrV3tFmDMsOIiqonZSRUrAU2a",
    avatar = "https://cdn.discordapp.com/attachments/1402746388080492544/1402747763321606286/image.png?ex=68950a28&is=6893b8a8&hm=aecdad3fc18789157c30a4079bd405a26156aed92d973e16a1273fee75343e84&",
    name = "Valic Housing System",
    color = 3447003, -- Blue color
    enabled = true
}

-- Security Configuration
ServerConfig.Security = {
    -- Maximum attempts for password entry
    maxPasswordAttempts = 3,
    -- Lockout time in minutes after max attempts
    lockoutTime = 10,
    -- Minimum time between password changes (minutes)
    passwordChangeInterval = 5,
    -- Tour code expiration time (minutes)
    tourExpiration = 10,
    -- Maximum concurrent tours per house
    maxConcurrentTours = 3
}

-- Payment Configuration
ServerConfig.Payment = {
    -- Resale percentage (30% of original price)
    resalePercentage = 0.30,
    -- Accepted payment methods
    acceptedMethods = {
        cash = true,
        bank = true
    },
    -- Currency symbol for logging
    currencySymbol = "$"
}

-- Framework Detection
ServerConfig.Framework = {
    -- Auto-detect framework (qbx_core, qb-core)
    autoDetect = true,
    -- Manual override (if autoDetect is false)
    override = "qbx_core" -- or "qb-core"
}

-- Database Configuration
ServerConfig.Database = {
    -- Cleanup interval for expired tours (minutes)
    cleanupInterval = 30,
    -- Log retention period (days)
    logRetention = 30
}

-- Admin Configuration
ServerConfig.Admin = {
    -- Admin groups that can manage all houses
    groups = {
        "admin",
        "superadmin"
    },
    -- Admin commands enabled
    commands = true
}
