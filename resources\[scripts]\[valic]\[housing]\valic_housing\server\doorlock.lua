-- Valic Housing System - Doorlock Integration
-- Author: stepan_valic

-- Password Management Functions (Global)
function UpdateDoorPasswords(doorIds, newPassword)
    if type(doorIds) == 'number' then
        doorIds = {doorIds}
    end

    -- Use the custom ox_doorlock event for batch password updates
    TriggerEvent('ox_doorlock:refreshPasswords', doorIds, newPassword)

    if Config.Debug then
        print(string.format('^3[Valic Housing]^7 Updated passwords for %d doors', #doorIds))
    end
end

-- Change house password
lib.callback.register('valic_housing:changePassword', function(source, houseId, currentPassword, newPassword)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end
    
    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end
    
    -- Check access
    if not HasHouseAccess(charid, houseId, 'keyholder') then
        return false, 'no_access'
    end
    
    -- Get house data
    local houseData = GetHouseData(houseId)
    if not houseData then return false, 'invalid_house' end
    
    -- Validate current password (only for non-owners)
    if houseData.owner_charid ~= charid and houseData.password ~= currentPassword then
        return false, 'invalid_current_password'
    end
    
    -- Check password change cooldown
    local lastChangeTimestamp = MySQL.scalar.await('SELECT UNIX_TIMESTAMP(last_password_change) FROM valic_housing WHERE house_id = ?', {houseId})
    if lastChangeTimestamp then
        local timeDiff = os.time() - lastChangeTimestamp
        local cooldownMinutes = ServerConfig.Security.passwordChangeInterval
        if timeDiff < (cooldownMinutes * 60) then
            local remainingMinutes = math.ceil((cooldownMinutes * 60 - timeDiff) / 60)
            return false, 'password_cooldown', {minutes = remainingMinutes}
        end
    end
    
    -- Validate new password
    if not newPassword or #newPassword < Config.MinPasswordLength or #newPassword > Config.MaxPasswordLength then
        return false, 'invalid_password'
    end
    
    if not string.match(newPassword, '^%d+$') then
        return false, 'password_not_numeric'
    end
    
    -- Update password in database
    MySQL.update('UPDATE valic_housing SET password = ?, last_password_change = NOW() WHERE house_id = ?', {
        newPassword, houseId
    })
    
    -- Update doorlock passwords
    local house = Config.Houses[houseId]
    if house and house.doorIds then
        UpdateDoorPasswords(house.doorIds, newPassword)
    end
    
    -- Log password change
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'password_change', json.encode({changed_by = charid})
    })
    
    -- Discord log
    SendDiscordLog('🔐 Password Changed', string.format('Password changed for house %d by %s', houseId, charid), 16776960, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Changed By', value = charid, inline = true}
    })
    
    return true, 'password_changed'
end)

-- End tour and reset passwords
local function EndTour(houseId, charid)
    -- Check if tour is already ended
    local activeTour = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE house_id = ? AND charid = ? AND expires_at > NOW() AND is_used = 0', {
        houseId, charid
    })

    if not activeTour[1] then
        if Config.Debug then
            print(string.format('^3[Valic Housing]^7 Tour for house %d by %s already ended or not found', houseId, charid))
        end
        return -- Tour already ended or doesn't exist
    end

    -- Mark tour as used
    local affected = MySQL.update.await('UPDATE valic_housing_tours SET is_used = 1 WHERE house_id = ? AND charid = ? AND expires_at > NOW() AND is_used = 0', {
        houseId, charid
    })

    if affected == 0 then
        if Config.Debug then
            print(string.format('^3[Valic Housing]^7 No active tour found to end for house %d by %s', houseId, charid))
        end
        return -- No tour was actually ended
    end

    -- Check if house is owned, if so restore owner's password, otherwise set random
    local houseData = GetHouseData(houseId)
    local house = Config.Houses[houseId]

    if house and house.doorIds then
        if houseData then
            -- House is owned, restore owner's password
            UpdateDoorPasswords(house.doorIds, houseData.password)
            if Config.Debug then
                print(string.format('^3[Valic Housing]^7 Tour ended for house %d, restored owner password', houseId))
            end
        else
            -- House is not owned, set random password
            local randomPassword = GenerateRandomCode(8)
            UpdateDoorPasswords(house.doorIds, randomPassword)
            if Config.Debug then
                print(string.format('^3[Valic Housing]^7 Tour ended for house %d, doors secured with random password', houseId))
            end
        end
    end
end

-- Tour timer management
CreateThread(function()
    while true do
        Wait(60000) -- Check every minute
        
        -- Get tours expiring in 2 minutes
        local expiringTours = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 2 MINUTE) AND is_used = 0')
        
        for _, tour in pairs(expiringTours) do
            local player = GetPlayerByCitizenId(tour.charid)
            if player then
                TriggerClientEvent('valic_housing:tourEnding', player.PlayerData.source, tour.house_id)
            end
        end
        
        -- End expired tours
        local expiredTours = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE expires_at < NOW() AND is_used = 0')

        for _, tour in pairs(expiredTours) do
            -- Mark as used first to prevent duplicate processing
            MySQL.update('UPDATE valic_housing_tours SET is_used = 1 WHERE id = ?', {tour.id})

            -- Then handle the tour ending
            EndTour(tour.house_id, tour.charid)

            local player = GetPlayerByCitizenId(tour.charid)
            if player then
                TriggerClientEvent('valic_housing:tourEnded', player.PlayerData.source, tour.house_id)
            end
        end
    end
end)

-- Helper function to get player by citizen ID
function GetPlayerByCitizenId(citizenid)
    -- Get framework variables from main.lua
    local QBX = exports.qbx_core
    local QBCore = exports['qb-core']:GetCoreObject()

    if GetResourceState('qbx_core') == 'started' then
        return QBX:GetPlayerByCitizenId(citizenid)
    elseif GetResourceState('qb-core') == 'started' then
        return QBCore.Functions.GetPlayerByCitizenId(citizenid)
    end
    return nil
end

-- Manual tour end (when player leaves area or via NPC)
RegisterNetEvent('valic_housing:endTour', function(houseId)
    local src = source
    local player = GetPlayer(src)
    if not player then return end

    local charid = GetCharacterId(player)
    if not charid then return end

    EndTour(houseId, charid)

    -- Clear client state immediately
    TriggerClientEvent('valic_housing:clearTourState', src)

    if Config.Debug then
        print(string.format('^3[Valic Housing]^7 Manual tour end for house %d by %s', houseId, charid))
    end
end)

-- Export doorlock functions
exports('UpdateDoorPasswords', UpdateDoorPasswords)
exports('EndTour', EndTour)
