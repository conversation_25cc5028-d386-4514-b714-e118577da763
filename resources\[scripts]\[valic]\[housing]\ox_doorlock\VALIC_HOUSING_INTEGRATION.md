# Valic Housing Integration - ox_doorlock Úpravy

Tento dokument popisuje úpravy provedené v ox_doorlock pro integraci s Valic Housing systémem.

## Přehled úprav

Ox_doorlock byl rozšířen o možnost dynamické aktualizace hesel dveří bez nutnosti restartu resource. Tyto úpravy umožňují Valic Housing systému měnit hesla dveří v reálném čase.

## Nové funkce

### 1. Event: `ox_doorlock:updatePasscode`

**Účel:** Aktualizuje heslo konkrétních dveří bez restartu

**Parametry:**
- `doorId` (number) - ID dveří v ox_doorlock databázi
- `newPasscode` (string) - Nové číselné heslo

**Použití:**
```lua
<PERSON>('ox_doorlock:updatePasscode', 67, '1234')
```

**Co se děje:**
1. Aktualizuje heslo v paměti (`doors[doorId].passcode`)
2. Uloží změnu do databáze (`ox_doorlock` tabulka)
3. Synchronizuje změnu se všemi klienty
4. Vypíše debug zprávu (pokud je zapnutý debug)

### 2. Event: `ox_doorlock:refreshPasswords`

**Účel:** Hromadná aktualizace hesel pro více dveří najednou

**Parametry:**
- `doorIds` (number|table) - ID dveří nebo pole ID dveří
- `newPasscode` (string) - Nové heslo pro všechny dveře

**Použití:**
```lua
-- Jednotlivé dveře
TriggerEvent('ox_doorlock:refreshPasswords', 67, '1234')

-- Více dveří najednou
TriggerEvent('ox_doorlock:refreshPasswords', {67, 68, 69, 70}, '1234')
```

**Co se děje:**
1. Převede jednotlivé ID na pole (pokud je potřeba)
2. Zavolá `ox_doorlock:updatePasscode` pro každé ID
3. Efektivní pro aktualizaci všech dveří jednoho domu

## Implementace v server/main.lua

### Umístění kódu
Kód byl přidán na konec souboru `server/main.lua` (řádky 348-380):

```lua
-- Valic Housing Integration: Update door passcode dynamically
RegisterNetEvent('ox_doorlock:updatePasscode', function(doorId, newPasscode)
    local door = doors[doorId]
    if not door then return end

    -- Update the door's passcode in memory
    door.passcode = newPasscode

    -- Update in database
    local data = json.decode(MySQL.Sync.fetchScalar('SELECT data FROM ox_doorlock WHERE id = ?', {doorId}))
    if data then
        data.passcode = newPasscode
        MySQL.Async.execute('UPDATE ox_doorlock SET data = ? WHERE id = ?', {json.encode(data), doorId})
    end

    -- Sync to all clients
    TriggerClientEvent('ox_doorlock:setState', -1, doorId, door.state, door)

    if Config.Debug then
        print(string.format("^3[ox_doorlock]^7 Updated passcode for door %s to: %s", doorId, newPasscode))
    end
end)

-- Valic Housing Integration: Refresh door passwords
RegisterNetEvent('ox_doorlock:refreshPasswords', function(doorIds, newPasscode)
    if type(doorIds) == 'number' then
        doorIds = {doorIds}
    end

    for _, doorId in pairs(doorIds) do
        TriggerEvent('ox_doorlock:updatePasscode', doorId, newPasscode)
    end
end)
```

## Použití v Valic Housing

### Při zahájení prohlídky
```lua
-- Nastavení dočasného kódu pro prohlídku
local tourCode = "1234"
local doorIds = {67, 68, 69, 70, 71, 72}
TriggerEvent('ox_doorlock:refreshPasswords', doorIds, tourCode)
```

### Při nákupu nemovitosti
```lua
-- Nastavení vlastního hesla po koupi
local playerPassword = "5678"
local doorIds = house.doorIds
TriggerEvent('ox_doorlock:refreshPasswords', doorIds, playerPassword)
```

### Při změně hesla
```lua
-- Změna hesla vlastníkem
local newPassword = "9999"
TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, newPassword)
```

### Při ukončení prohlídky
```lua
-- Nastavení náhodného hesla po ukončení prohlídky
local randomPassword = GenerateRandomCode(8)
TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, randomPassword)
```

## Bezpečnostní aspekty

### 1. Validace vstupů
- Funkce kontroluje existenci dveří před aktualizací
- Neplatné `doorId` je ignorováno bez chyby

### 2. Databázová konzistence
- Změny se ukládají jak do paměti, tak do databáze
- Zajišťuje persistenci hesel při restartu

### 3. Synchronizace klientů
- Všichni klienti jsou okamžitě informováni o změně
- Žádné zpoždění při použití nového hesla

## Debug a troubleshooting

### Zapnutí debug režimu
V `config.lua` ox_doorlock nastavte:
```lua
Config.Debug = true
```

### Debug výstupy
```
[ox_doorlock] Updated passcode for door 67 to: 1234
[ox_doorlock] Updated passcode for door 68 to: 1234
```

### Časté problémy

1. **Heslo se neaktualizuje**
   - Zkontrolujte správnost `doorId`
   - Ověřte, že dveře existují v databázi
   - Zapněte debug režim pro více informací

2. **Klienti nevidí změnu**
   - Problém se synchronizací
   - Zkontrolujte network eventy
   - Restartujte ox_doorlock

3. **Databázové chyby**
   - Zkontrolujte MySQL připojení
   - Ověřte strukturu `ox_doorlock` tabulky

## Kompatibilita

### Verze ox_doorlock
- Testováno s nejnovější verzí ox_doorlock
- Vyžaduje MySQL/MariaDB databázi
- Kompatibilní s existujícími dveřmi

### Zpětná kompatibilita
- Úpravy neovlivňují stávající funkcionalitu
- Existující dveře fungují bez změn
- Nové eventy jsou volitelné

## Instalace úprav

1. **Zálohujte původní soubor**
   ```bash
   cp server/main.lua server/main.lua.backup
   ```

2. **Přidejte kód na konec `server/main.lua`**
   - Zkopírujte kód z implementace výše

3. **Restartujte ox_doorlock**
   ```
   restart ox_doorlock
   ```

4. **Testujte funkcionalita**
   ```lua
   TriggerEvent('ox_doorlock:updatePasscode', 1, '1234')
   ```

## Podpora

Pro podporu a hlášení problémů s integrací kontaktujte autora Valic Housing systému.

**Autor úprav:** stepan_valic  
**Datum:** 2025-01-07  
**Verze:** 1.0.0
