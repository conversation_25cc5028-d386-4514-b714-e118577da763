return {
    ---------Chopshop
    -- Vehicle Doors
    ['car_door'] = {
        label = 'Car Door',
        weight = 15000, -- 15kg
        stack = true,
        close = true,
        description = 'A salvaged car door that can be sold or used for repairs',
        client = {
            image = 'car_door.png',
        }
    },

    -- Vehicle Wheels
    ['car_wheel'] = {
        label = 'Car Wheel',
        weight = 20000, -- 20kg
        stack = true,
        close = true,
        description = 'A salvaged car wheel with tire',
        client = {
            image = 'car_wheel.png',
        }
    },

    -- Vehicle Hood
    ['car_hood'] = {
        label = 'Car Hood',
        weight = 25000, -- 25kg
        stack = true,
        close = true,
        description = 'A salvaged car hood, may contain valuable engine components',
        client = {
            image = 'car_hood.png',
        }
    },

    -- Vehicle Trunk
    ['car_trunk'] = {
        label = 'Car Trunk',
        weight = 20000, -- 20kg
        stack = true,
        close = true,
        description = 'A salvaged car trunk lid',
        client = {
            image = 'car_trunk.png',
        }
    },

    -- Bonus items that might be found
    ['car_bumper'] = {
        label = 'Car Bumper',
        weight = 12000, -- 12kg
        stack = true,
        close = true,
        description = 'A salvaged car bumper',
        client = {
            image = 'car_bumper.png',
        }
    },

    ['car_mirror'] = {
        label = 'Side Mirror',
        weight = 2000, -- 2kg
        stack = true,
        close = true,
        description = 'A salvaged side mirror',
        client = {
            image = 'car_mirror.png',
        }
    },

    ['car_headlight'] = {
        label = 'Headlight',
        weight = 3000, -- 3kg
        stack = true,
        close = true,
        description = 'A salvaged headlight assembly',
        client = {
            image = 'car_headlight.png',
        }
    },

    ['car_taillight'] = {
        label = 'Taillight',
        weight = 2500, -- 2.5kg
        stack = true,
        close = true,
        description = 'A salvaged taillight assembly',
        client = {
            image = 'car_taillight.png',
        }
    },

    ---------remiix
    -- Tint Meter
    ['tintmeter'] = {
        label = 'Tint Meter',
        weight = 800,
        stack = false,
        description = 'Tint meter pro kontrolování zatmavení oken',
        client = {
            export = 'drf-tintmeter.TintMeter'
        }
    },
    ---------beriegerry
    -- Spray Can Item
    ['spray_can'] = {
        label = 'Spray Can',
        weight = 500,
        stack = true,
        close = true,
        description = 'A can of spray paint for marking territory',
        client = {
            image = 'spray_can.png',
            --usetime = 2500,
        }
        --,
        -- server = {
        --     export = 'br_territories.useSprayItem'
        -- }
    },

    -- Spray Cleaner Item
    ['spray_cleaner'] = {
        label = 'Spray Cleaner',
        weight = 300,
        stack = true,
        close = true,
        description = 'Chemical cleaner for removing spray paint',
        client = {
            image = 'spray_cleaner.png',
            --usetime = 2000,
        }
        --,
        -- server = {
        --     export = 'br_territories.useCleanerItem'
        -- }
    },

    -- Gang Creation Kit (Optional)
    ['gang_creation_kit'] = {
        label = 'Gang Creation Kit',
        weight = 1000,
        stack = false,
        close = true,
        description = 'Everything needed to start a new gang',
        client = {
            image = 'gang_kit.png',
            --usetime = 5000,
        }
        --,
        -- server = {
        --     export = 'br_territories.useGangKit'
        -- }
    },
    ---------beriegerry-END

    --stepan speje start

    ['spray_red'] = {
        label = 'Červený spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Červený sprej na graffiti',
        client = {
            image = 'spray_red.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ['spray_blue'] = {
        label = 'Modrý spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Modrý sprej na graffiti',
        client = {
            image = 'spray_blue.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ['spray_green'] = {
        label = 'Zelený spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Zelený sprej na graffiti',
        client = {
            image = 'spray_green.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ['spray_yellow'] = {
        label = 'Žlutý spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Žlutý sprej na graffiti',
        client = {
            image = 'spray_yellow.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ['spray_black'] = {
        label = 'Černý spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Černý sprej na graffiti',
        client = {
            image = 'spray_black.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ['spray_white'] = {
        label = 'Bílý spray',
        weight = 200,
        stack = true,
        close = true,
        description = 'Bílý sprej na graffiti',
        client = {
            image = 'spray_white.png',
            export = 'valic_sprays.useSpray'
        }
    },

    ----stepan spreje done
    ['testburger'] = {
        label = 'Test Burger',
        weight = 220,
        degrade = 60,
        client = {
            image = 'burger_chicken.png',
            status = { hunger = 200000 },
            anim = 'eating',
            prop = 'burger',
            usetime = 2500,
            export = 'ox_inventory_examples.testburger'
        },
        server = {
            export = 'ox_inventory_examples.testburger',
            test = 'what an amazingly delicious burger, amirite?'
        },
        buttons = {
            {
                label = 'Lick it',
                action = function(slot)
                    print('You licked the burger')
                end
            },
            {
                label = 'Squeeze it',
                action = function(slot)
                    print('You squeezed the burger :(')
                end
            },
            {
                label = 'What do you call a vegan burger?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('A misteak.')
                end
            },
            {
                label = 'What do frogs like to eat with their hamburgers?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('French flies.')
                end
            },
            {
                label = 'Why were the burger and fries running?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('Because they\'re fast food.')
                end
            }
        },
        consume = 0.3
    },

    ------------------------------------------------scripts---------------------------

    ------------------------------------------------crafting---------------------------

    ['pure_bench'] = {
        label = 'Stůl pro výrobu',
        weight = 600,
        stack = false,
    },



    ------------------------------------------------coke---------------------------

    ['ls_coke_table'] = {
        label = 'Coke Table',
        weight = 1000,
    },

    ['ls_coca_seed'] = {
        label = 'Coca Seed',
        weight = 5,
    },

    ['ls_coca_leaf'] = {
        label = 'Coca Leaf',
        weight = 5,
    },

    ['ls_coca_ground'] = {
        label = 'Ground Coca',
        weight = 20,
    },  

    ['ls_coca_base_unf'] = {
        label = 'Coca Base (unfinished)',
        weight = 40,
    },

    ['ls_coca_base'] = {
        label = 'Coca Base',
        weight = 50,
    },

    ['ls_cocaine_brick'] = {
        label = 'Cocaine Brick',
        weight = 100,
    },

    ['ls_crack_brick'] = {
        label = 'Crack Brick',
        weight = 100,
    },

    ['ls_baking_soda'] = {
        label = 'Baking Soda',
        weight = 25,
    },

    ['ls_gasoline'] = {
        label = 'Gasoline',
        weight = 1000,
        stack = false,
    },

    ['ls_cement'] = {
        label = 'Cement',
        weight = 2000,
        stack = false,
    },

    ['ls_cocaine_bag'] = {
        label = 'Cocaine',
        weight = 10,
    },

    ['ls_crack_bag'] = {
        label = 'Crack',
        weight = 10,
    },

    ---------------------------------------------rental---------------------------

    ["rentalpapers"] = {
        label = "Papíry od auta",
        weight = 0,
        stack = true,
    },

    ---------------------------------------------outfit bag---------------------------

	['outfitbag'] = {
		label = 'Outfitbag',
		consume = 0,
		weight = 1,
		client = {
			export = 'krs_outfitbag.outfitbag'
		}
	},

    ---------------------------------------------jewels heist---------------------------
    
    ["jewels"] = {
        label = "Klenoty",
        weight = 10,
        stack = true,
    },

    ["ruby"] = {
        label = "Červený rubín",
        weight = 100,
    },

    ["panther"] = {
        label = "Socha Pantera",
        weight = 100,
    },

    ["ruby_necklace"] = {
        label = "Rubínový náhrdelník",
        weight = 100,
    },

    ["emerald"] = {
        label = "Smaragd",
        weight = 100,
    },

    ["golden_banana"] = {
        label = "Zlatý banán",
        weight = 100,
    },

    ["cable_cutter"] = {
        label = "Řezačka kabelů",
        weight = 1,
        stack = true,
    },

    ["glass_cutter"] = {
        label = "Řezačka skla",
        weight = 500,
        stack = true,
    },

    ["screwdriver"] = {
        label = "Šroubovák",
        weight = 50,
        stack = true,
    },

    ---------------------------------------------meth---------------------------

    ['ls_meth_table'] = {
	    label = 'Meth Table',
	    weight = 1000,
	    stack = false
    },

    ['ls_gas_mask'] = {
	    label = 'Gas Mask',
    	weight = 150,
	    stack = false
    },

    ['ls_pseudoephedrine'] = {
    	label = 'Pseudoephedrine Pills',
    	weight = 50
    },

    ['ls_crushed_pseudoephedrine'] = {
    	label = 'Crushed Pseudoephedrine',
    	weight = 25
    },

    ['ls_ammonia'] = {
    	label = 'Ammonia',
    	weight = 250,
    	stack = false
    },

    ['ls_iodine'] = {
    	label = 'Iodine',
    	weight = 250,
    	stack = false
    },

    ['ls_acetone'] = {
    	label = 'Acetone',
    	weight = 250,
    	stack = false
    },

    ['ls_liquid_meth'] = {
    	label = 'Liquid Meth',
    	weight = 225
    },

    ['ls_hydrochloric_acid'] = {
    	label = 'Hydrochloric Acid',
    	weight = 250
    },

    ['ls_meth'] = {
    	label = 'Meth',
    	weight = 50
    },

    ['ls_supply_crate'] = {
    	label = 'Supplies',
    	weight = 1000
    },

    ['ls_meth_tray'] = {
    	label = 'Meth Tray',
    	weight = 50
    },

    ['ls_meth_box'] = {
    	label = 'Meth Box',
    	weight = 50
    },

    ---------------------------------------------weed---------------------------

    ['ls_plain_jane_seed'] = {
        label = 'Plain Jane Seed',
        weight = 5,
    },

    ['ls_plain_jane_bud'] = {
        label = 'Plain Jane Bud',
        weight = 5,
    },

    ['ls_plain_jane_bag'] = {
        label = 'Plain Jane Bag',
        weight = 10,
    },

    ['ls_plain_jane_joint'] = {
        label = 'Plain Jane Joint',
        weight = 10,
    },

    ['ls_banana_kush_seed'] = {
        label = 'Banana Kush Seed',
        weight = 5,
    },

    ['ls_banana_kush_bud'] = {
        label = 'Banana Kush Bud',
        weight = 5,
    },

    ['ls_banana_kush_bag'] = {
        label = 'Banana Kush Bag',
        weight = 10,
    },

    ['ls_banana_kush_joint'] = {
        label = 'Banana Kush Joint',
        weight = 10,
    },

    ['ls_blue_dream_seed'] = {
        label = 'Blue Dream Seed',
        weight = 5,
    },

    ['ls_blue_dream_bud'] = {
        label = 'Blue Dream Bud',
        weight = 5,
    },

    ['ls_blue_dream_bag'] = {
        label = 'Blue Dream Bag',
        weight = 10,
    },  

    ['ls_blue_dream_joint'] = {
        label = 'Blue Dream Joint',
        weight = 10,
    },

    ['ls_purple_haze_seed'] = {
        label = 'Purple Haze Seed',
        weight = 5,
    },

    ['ls_purple_haze_bud'] = {
        label = 'Purple Haze Bud',
        weight = 5,
    },

    ['ls_purple_haze_bag'] = {
        label = 'Purple Haze Bag',
        weight = 10,
    },

    ['ls_purple_haze_joint'] = {
        label = 'Purple Haze Joint',
        weight = 10,
    },

    ['ls_orange_crush_seed'] = {
        label = 'Orange Crush Seed',
        weight = 5,
    },

    ['ls_orange_crush_bud'] = {
        label = 'Orange Crush Bud',
        weight = 5,
    },

    ['ls_orange_crush_bag'] = {
        label = 'Orange Crush Bag',
        weight = 10,
    },

    ['ls_orange_crush_joint'] = {
        label = 'Orange Crush Joint',
        weight = 10,
    },

    ['ls_cosmic_kush_seed'] = {
        label = 'Cosmic Kush Seed',
        weight = 5,
    },

    ['ls_cosmic_kush_bud'] = {
        label = 'Cosmic Kush Bud',
        weight = 5,
    },

    ['ls_cosmic_kush_bag'] = {
        label = 'Cosmic Kush Bag',
        weight = 10,
    },

    ['ls_cosmic_kush_joint'] = {
        label = 'Cosmic Kush Joint',
        weight = 10,
    },

    ['ls_rolling_paper'] = {
        label = 'Rolling Paper',
        weight = 5,
    },

    ['ls_empty_baggy'] = {
        label = 'Empty Baggy',
        weight = 5,
    },

    ['ls_access_card'] = {
        label = 'Access Card',
        weight = 15,
    },

    ['ls_watering_can'] = {
        label = 'Watering Can',
        weight = 3250,
        stack = false,
    },

    ['ls_fertilizer'] = {
        label = 'Fertilizer',
        weight = 1750,
        stack = false,
    },

    ['ls_plant_pot'] = {
        label = 'Plant Pot',
        weight = 25,
    },

    ['ls_shovel'] = {
        label = 'Shovel',
        weight = 75,
    },

    ['ls_shears'] = {
        label = 'Shears',
        weight = 10,
    },

    ['ls_weed_table'] = {
    	label = 'Weed Table',
	    weight = 1000,
	    stack = false
    },

    ---------------------------------------------money lau---------------------------

    ['warehouse_key'] = {
        label = 'Klíč od pračky',
        weight = 0,
        stack = false,
        close = true,
    },    

    ['uncounted_money'] = {
        label = 'Nespočítané peníze',
        weight = 0,
        stack = true,
        close = true,
    }, 
    
    ---------------------------------------------fleeca---------------------------

    ['usb_fleeca'] = {
        label = 'Fleeca usb',
        weight = 100,
        stack = false,
        close = true,
    },

    ['crfleecacard'] = {
        label = 'Karta od trezoru',
        weight = 100,
        stack = false,
        close = true,
    },    

    ['printed_document'] = {
        label = 'Duležitý dokument',
        weight = 100,
        stack = false,
        close = true,
    },

    ---------------------------------------------boosting---------------------------

    ['boostingtablet'] = {
        label = 'Boosting tablet',
        weight = 0,
        description = "Seems like something's installed on this.",
        client = {
	    export = 'rahe-boosting.boostingtablet',
        }
    },

    ['hackingdevice'] = {
        label = 'Hacking device',
        weight = 0,
        description = 'Will allow you to bypass vehicle security systems.',
        client = {
	    export = 'rahe-boosting.hackingdevice',
        }
    },

    ['gpshackingdevice'] = {
        label = 'GPS hacking device',
        weight = 0,
        description = 'If you wish to disable vehicle GPS systems.',
        client = {
	    export = 'rahe-boosting.gpshackingdevice',
        }
    },

    ---------------------------------------------evidence---------------------------

    ['uvlight'] = {
        label = 'Uv světlo',
        weight = 120,
        stack = false,
    },

    ['bleachwipes'] = {
        label = 'Čistící prostředky',
        weight = 150,
        stack = false,
    },

    ---------------------------------------------crutch---------------------------

    ['crutch'] = {
        label = 'Berle',
        weight = 500,
        stack = false,
    },

    ['wheelchair'] = {
        label = 'Invalidní vozík',
        weight = 1000,
        stack = false,
    },

    ---------------------------------------------ambulancejob---------------------------

    ['vic10'] = {
        label = 'Vicodin 10mg',
        weight = 10,
        stack = true,
    },

    ['vic5'] = {
        label = 'Vicodin 5mg',
        weight = 5,
        stack = true,
    },

    ['tweezers'] = {
        label = 'Pinzeta',
        weight = 30,
        stack = true,
    },

    ['suturekit'] = {
        label = 'Šicí sada',
        weight = 100,
        stack = true,
    },

    ['stretcher'] = {
        label = 'Nosítka',
        weight = 1500,
        stack = false,
    },

    ['sedative'] = {
        label = 'Sedativa',
        weight = 50,
        stack = true,
    },

    ['perc30'] = {
        label = 'Percocet 30mg',
        weight = 30,
        stack = true,
    },

    ['perc10'] = {
        label = 'Percocet 10mg',
        weight = 10,
        stack = true,
    },

    ['perc5'] = {
        label = 'Percocet 5mg',
        weight = 5,
        stack = true,
    },

    ['morphine30'] = {
        label = 'Morfin 30mg',
        weight = 30,
        stack = true,
    },

    ['morphine15'] = {
        label = 'Morfin 15mg',
        weight = 15,
        stack = true,
    },
    
    ['medikit'] = {
        label = 'Lékárnička',
        weight = 200,
        stack = true,
    },

    ['medbag'] = {
        label = 'Lékařská taška',
        weight = 1000,
        stack = false,
    },
    
    ['icepack'] = {
        label = 'Led',
        weight = 100,
        stack = true,
    },


    ['defib'] = {
        label = 'Defibrilátor',
        weight = 500,
        stack = true,
    },

    ['burncream'] = {
        label = 'Vypalovací krém',
        weight = 50,
        stack = true,
    },
     
    ---------------------------------------jail--------------------------------

    ['prison_tablet'] = {
        label = 'Jail tablet',
        weight = 100,
        stack = false,
    },

    ['wire_cutter'] = {
        label = 'Kleště',
        weight = 100,
        stack = false,
    },

    ['cigarrete'] = {
        label = 'Cigareta',
        weight = 0,
    },

    -----------------------------------policejob-------------------------------

    ['tracking_bracelet'] = {
        label = 'Monitorovací nárame',
        weight = 50,
        stack = false,
    },

    --------------------------------------zaklad---------------------------------

    ['bandage'] = {
        label = 'Bandage',
        weight = 115,
    },

    ['burger'] = {
        label = 'Burger',
        weight = 220,
        client = {
            status = { hunger = 200000 },
            anim = 'eating',
            prop = 'burger',
            usetime = 2500,
            notification = 'You ate a delicious burger'
        },
    },

    ['sprunk'] = {
        label = 'Sprunk',
        weight = 350,
        client = {
            status = { thirst = 200000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_ld_can_01`, pos = vec3(0.01, 0.01, 0.06), rot = vec3(5.0, 5.0, -180.5) },
            usetime = 2500,
            notification = 'You quenched your thirst with a sprunk'
        }
    },

    ['parachute'] = {
        label = 'Parachute',
        weight = 8000,
        stack = false,
        client = {
            anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
            usetime = 1500
        }
    },

    ['garbage'] = {
        label = 'Garbage',
    },

    ['paperbag'] = {
        label = 'Paper Bag',
        weight = 1,
        stack = false,
        close = false,
        consume = 0
    },

    ['panties'] = {
        label = 'Knickers',
        weight = 10,
        consume = 0,
        client = {
            status = { thirst = -100000, stress = -25000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_cs_panties_02`, pos = vec3(0.03, 0.0, 0.02), rot = vec3(0.0, -13.5, -1.5) },
            usetime = 2500,
        }
    },

    ['lockpick'] = {
        label = 'Lockpick',
        weight = 160,
    },

    ['phone'] = {
        label = 'Phone',
        weight = 190,
        stack = false,
        consume = 0,
        client = {
            add = function(total)
                if total > 0 then
                    pcall(function() return exports.npwd:setPhoneDisabled(false) end)
                end
            end,

            remove = function(total)
                if total < 1 then
                    pcall(function() return exports.npwd:setPhoneDisabled(true) end)
                end
            end
        }
    },

    ['mustard'] = {
        label = 'Mustard',
        weight = 500,
        client = {
            status = { hunger = 25000, thirst = 25000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_food_mustard`, pos = vec3(0.01, 0.0, -0.07), rot = vec3(1.0, 1.0, -1.5) },
            usetime = 2500,
            notification = 'You... drank mustard'
        }
    },

    ['water'] = {
        label = 'Water',
        weight = 500,
        client = {
            status = { thirst = 200000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_ld_flow_bottle`, pos = vec3(0.03, 0.03, 0.02), rot = vec3(0.0, 0.0, -1.5) },
            usetime = 2500,
            cancel = true,
            notification = 'You drank some refreshing water'
        }
    },

    ['armour'] = {
        label = 'Bulletproof Vest',
        weight = 3000,
        stack = false,
        client = {
            anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
            usetime = 3500
        }
    },

    ['clothing'] = {
        label = 'Clothing',
        consume = 0,
    },

    ['money'] = {
        label = 'Money',
    },

    ['black_money'] = {
        label = 'Dirty Money',
    },

    ['id_card'] = {
        label = 'Identification Card',
    },

    ['driver_license'] = {
        label = 'Drivers License',
    },

    ['weaponlicense'] = {
        label = 'Weapon License',
    },

    ['lawyerpass'] = {
        label = 'Lawyer Pass',
    },

    ['radio'] = {
        label = 'Radio',
        weight = 1000,
        allowArmed = true,
        consume = 0,
        client = {
            event = 'mm_radio:client:use'
        }
    },

    ['jammer'] = {
        label = 'Radio Jammer',
        weight = 10000,
        allowArmed = true,
        client = {
            event = 'mm_radio:client:usejammer'
        }
    },

    ['radiocell'] = {
        label = 'AAA Cells',
        weight = 1000,
        stack = true,
        allowArmed = true,
        client = {
            event = 'mm_radio:client:recharge'
        }
    },

    ['advancedlockpick'] = {
        label = 'Advanced Lockpick',
        weight = 500,
    },

    ['screwdriverset'] = {
        label = 'Screwdriver Set',
        weight = 500,
    },

    ['electronickit'] = {
        label = 'Electronic Kit',
        weight = 500,
    },

    ['cleaningkit'] = {
        label = 'Cleaning Kit',
        weight = 500,
    },

    ['repairkit'] = {
        label = 'Repair Kit',
        weight = 2500,
    },

    ['advancedrepairkit'] = {
        label = 'Advanced Repair Kit',
        weight = 4000,
    },

    ['diamond_ring'] = {
        label = 'Diamond',
        weight = 1500,
    },

    ['rolex'] = {
        label = 'Golden Watch',
        weight = 1500,
    },

    ['goldbar'] = {
        label = 'Gold Bar',
        weight = 1500,
    },

    ['goldchain'] = {
        label = 'Golden Chain',
        weight = 1500,
    },

    ['crack_baggy'] = {
        label = 'Crack Baggy',
        weight = 100,
    },

    ['cokebaggy'] = {
        label = 'Bag of Coke',
        weight = 100,
    },

    ['coke_brick'] = {
        label = 'Coke Brick',
        weight = 2000,
    },

    ['coke_small_brick'] = {
        label = 'Coke Package',
        weight = 1000,
    },

    ['xtcbaggy'] = {
        label = 'Bag of Ecstasy',
        weight = 100,
    },

    ['meth'] = {
        label = 'Methamphetamine',
        weight = 100,
    },

    ['oxy'] = {
        label = 'Oxycodone',
        weight = 100,
    },

    ['weed_ak47'] = {
        label = 'AK47 2g',
        weight = 200,
    },

    ['weed_ak47_seed'] = {
        label = 'AK47 Seed',
        weight = 1,
    },

    ['weed_skunk'] = {
        label = 'Skunk 2g',
        weight = 200,
    },

    ['weed_skunk_seed'] = {
        label = 'Skunk Seed',
        weight = 1,
    },

    ['weed_amnesia'] = {
        label = 'Amnesia 2g',
        weight = 200,
    },

    ['weed_amnesia_seed'] = {
        label = 'Amnesia Seed',
        weight = 1,
    },

    ['weed_og-kush'] = {
        label = 'OGKush 2g',
        weight = 200,
    },

    ['weed_og-kush_seed'] = {
        label = 'OGKush Seed',
        weight = 1,
    },

    ['weed_white-widow'] = {
        label = 'OGKush 2g',
        weight = 200,
    },

    ['weed_white-widow_seed'] = {
        label = 'White Widow Seed',
        weight = 1,
    },

    ['weed_purple-haze'] = {
        label = 'Purple Haze 2g',
        weight = 200,
    },

    ['weed_purple-haze_seed'] = {
        label = 'Purple Haze Seed',
        weight = 1,
    },

    ['weed_brick'] = {
        label = 'Weed Brick',
        weight = 2000,
    },

    ['weed_nutrition'] = {
        label = 'Plant Fertilizer',
        weight = 2000,
    },

    ['joint'] = {
        label = 'Joint',
        weight = 200,
    },

    ['rolling_paper'] = {
        label = 'Rolling Paper',
        weight = 0,
    },

    ['empty_weed_bag'] = {
        label = 'Empty Weed Bag',
        weight = 0,
    },

    ['firstaid'] = {
        label = 'First Aid',
        weight = 2500,
    },

    ['ifaks'] = {
        label = 'Individual First Aid Kit',
        weight = 2500,
    },

    ['painkillers'] = {
        label = 'Painkillers',
        weight = 400,
    },

    ['firework1'] = {
        label = '2Brothers',
        weight = 1000,
    },

    ['firework2'] = {
        label = 'Poppelers',
        weight = 1000,
    },

    ['firework3'] = {
        label = 'WipeOut',
        weight = 1000,
    },

    ['firework4'] = {
        label = 'Weeping Willow',
        weight = 1000,
    },

    ['steel'] = {
        label = 'Steel',
        weight = 100,
    },

    ['rubber'] = {
        label = 'Rubber',
        weight = 100,
    },

    ['metalscrap'] = {
        label = 'Metal Scrap',
        weight = 100,
    },

    ['iron'] = {
        label = 'Iron',
        weight = 100,
    },

    ['copper'] = {
        label = 'Copper',
        weight = 100,
    },

    ['aluminium'] = {
        label = 'Aluminium',
        weight = 100,
    },

    ['plastic'] = {
        label = 'Plastic',
        weight = 100,
    },

    ['glass'] = {
        label = 'Glass',
        weight = 100,
    },

    ['gatecrack'] = {
        label = 'Gatecrack',
        weight = 1000,
    },

    ['cryptostick'] = {
        label = 'Crypto Stick',
        weight = 100,
    },

    ['trojan_usb'] = {
        label = 'Trojan USB',
        weight = 100,
    },

    ['toaster'] = {
        label = 'Toaster',
        weight = 5000,
    },

    ['small_tv'] = {
        label = 'Small TV',
        weight = 100,
    },

    ['security_card_01'] = {
        label = 'Security Card A',
        weight = 100,
    },

    ['security_card_02'] = {
        label = 'Security Card B',
        weight = 100,
    },

    ['drill'] = {
        label = 'Drill',
        weight = 5000,
    },

    ['thermite'] = {
        label = 'Thermite',
        weight = 1000,
    },

    ['diving_gear'] = {
        label = 'Diving Gear',
        weight = 30000,
    },

    ['diving_fill'] = {
        label = 'Diving Tube',
        weight = 3000,
    },

    ['antipatharia_coral'] = {
        label = 'Antipatharia',
        weight = 1000,
    },

    ['dendrogyra_coral'] = {
        label = 'Dendrogyra',
        weight = 1000,
    },

    ['jerry_can'] = {
        label = 'Jerrycan',
        weight = 3000,
    },

    ['nitrous'] = {
        label = 'Nitrous',
        weight = 1000,
    },

    ['wine'] = {
        label = 'Wine',
        weight = 500,
    },

    ['grape'] = {
        label = 'Grape',
        weight = 10,
    },

    ['grapejuice'] = {
        label = 'Grape Juice',
        weight = 200,
    },

    ['coffee'] = {
        label = 'Coffee',
        weight = 200,
    },

    ['vodka'] = {
        label = 'Vodka',
        weight = 500,
    },

    ['whiskey'] = {
        label = 'Whiskey',
        weight = 200,
    },

    ['beer'] = {
        label = 'beer',
        weight = 200,
    },

    ['sandwich'] = {
        label = 'beer',
        weight = 200,
    },

    ['walking_stick'] = {
        label = 'Walking Stick',
        weight = 1000,
    },

    ['lighter'] = {
        label = 'Lighter',
        weight = 200,
    },

    ['binoculars'] = {
        label = 'Binoculars',
        weight = 800,
    },

    ['stickynote'] = {
        label = 'Sticky Note',
        weight = 0,
    },

    ['empty_evidence_bag'] = {
        label = 'Empty Evidence Bag',
        weight = 200,
    },

    ['filled_evidence_bag'] = {
        label = 'Filled Evidence Bag',
        weight = 200,
    },

    ['harness'] = {
        label = 'Harness',
        weight = 200,
    },

    ['handcuffs'] = {
        label = 'Handcuffs',
        weight = 200,
    },

    -- // Business Items // --
 
    ['business_tempitem'] = {
        label = "how did you get this?",
        weight = 0,
        stack = true,
        close = true,
        consume = 0,
        server = {
            export = 'Renewed-Businesses.useItem',
        }
    },
    
    -- Kitchen Tools --
    
    ['kitchenknife'] = {
        label = 'Kitchen Knife',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['cleaver'] = {
        label = 'Meat Cleaver',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['blender'] = {
        label = 'Blender',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['whisk'] = {
        label = 'Whisks',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['slicer'] = {
        label = 'Slicer',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['potatopusher'] = {
        label = 'Potato Pusher',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['peeler'] = {
        label = 'Peeler',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['scooper'] = {
        label = 'Scooper',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    ['grater'] = {
        label = 'Grater',
        weight = 50,  
        shopType = 'general',
        price = 10,  
    },
    
    -- Fruit --
    
    ['strawberry'] = {
        label = 'Strawberries',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutstrawberry'] = {
        label = 'Cut Strawberries',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['strawberryjuice'] = {
        label = 'Strawberry Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['apples'] = {
        label = 'Apples',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutapples'] = {
        label = 'Cut Apples',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['applejuice'] = {
        label = 'Apple Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['pickle'] = {
        label = 'Pickles',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutpickle'] = {
        label = 'Cut Pickles',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['pineapple'] = {
        label = 'Pineapples',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutpineapple'] = {
        label = 'Cut Pineapple',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['pineapplejuice'] = {
        label = 'Pineapple Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['orange'] = {
        label = 'Oranges',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutorange'] = {
        label = 'Cut Oranges',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['orangejuice'] = {
        label = 'Orange Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['blueberry'] = {
        label = 'Blueberries',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutblueberry'] = {
        label = 'Cut Blueberries',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['blueberryjuice'] = {
        label = 'Blueberry Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['boba'] = {
        label = 'Boba',
        weight = 50,   
        foodType = 'food',
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['lime'] = {
        label = 'Limes',
        weight = 50,  
        shopType = 'farmers',
        price = 10,  
    },
    
    ['cutlime'] = {
        label = 'Cut Limes',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['limejuice'] = {
        label = 'Lime Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['banana'] = {
        label = 'Bananas',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        }, 
    },
    
    ['cutbananas'] = {
        label = 'Cut Bananas',
        weight = 50,   
        foodType = {'food', 'drink'},
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['grapes'] = {
        label = 'Grapes',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        }, 
    },
    
    ['grapejuice'] = {
        label = 'Grape Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['lemons'] = {
        label = 'Lemons',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['lemonjuice'] = {
        label = 'Lemon Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['cutlemon'] = {
        label = 'Cut Lemon',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    
    ['kiwi'] = {
        label = 'Kiwi',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['cutkiwi'] = {
        label = 'Cut Kiwi',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['cherry'] = {
        label = 'Cherries',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['cherryjuice'] = {
        label = 'Cherry Juice',
        weight = 50,   
        foodType = 'drink',
        nutrition = {
            healthy = 3,
            thirst = 3,
        },
    },
    
    ['lettuce'] = {
        label = 'Lettuce Head',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['choplettuce'] = {
        label = 'Chopped Lettuce',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['tomato'] = {
        label = 'Tomatos',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['choptomato'] = {
        label = 'Chopped Tomato',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['slicedtomato'] = {
        label = 'Tomato Slices',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['potatoes'] = {
        label = 'Potatoes',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['potatoslice'] = {
        label = 'Sliced Potatoes',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            salt = 2,
            hunger = 3,
        },
    },
    
    ['potatoskins'] = {
        label = 'Potato Skins',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            salt = 2,
            hunger = 3,
        },
    },
    
    ['choppotato'] = {
        label = 'Chopped Potatoes',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            salt = 2,
            hunger = 3,
        },
    },
    
    ['squash'] = {
        label = 'Squash',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopsquash'] = {
        label = 'Chopped Squash',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['spinach'] = {
        label = 'Spinach',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopspinach'] = {
        label = 'Chopped Spinach',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['celery'] = {
        label = 'Celery',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopcelery'] = {
        label = 'Chopped Celery',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['redpeppers'] = {
        label = 'Red Peppers',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['slicedredpepper'] = {
        label = 'Sliced Red Pepper',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['chopredpepper'] = {
        label = 'Chopped Red Pepper',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['greenpeppers'] = {
        label = 'Green Peppers',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['slicedgreenpepper'] = {
        label = 'Sliced Green Pepper',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['chopgreenpepper'] = {
        label = 'Chopped Green Pepper',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['hotpepper'] = {
        label = 'Jalapeno Peppers',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chophotpepper'] = {
        label = 'Chopped Jalapeno Pepper',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['carrots'] = {
        label = 'Carrots',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopcarrots'] = {
        label = 'Chopped Carrots',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['cucumbers'] = {
        label = 'Cucumbers',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopcucumbers'] = {
        label = 'Chopped Cucumbers',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['peas'] = {
        label = 'Peas',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['greenbeans'] = {
        label = 'Grean Beans',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['corn'] = {
        label = 'Corn',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['cobcorn'] = {
        label = 'Corn on the Cob',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    ['broccoli'] = {
        label = 'Broccoli',
        weight = 50,  
        shopType = 'farmers',
        price = 10, 
    },
    
    ['chopbroccoli'] = {
        label = 'Chopped Broccoli',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            healthy = 3,
            hunger = 3,
        },
    },
    
    -- // Dairy // --
    
    ['milk'] = {
        label = 'Milk',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = {'food', 'drink'},
        nutrition = {
            dairy = 3,
            thirst = 3,
        },
    },
    
    ['eggs'] = {
        label = 'Eggs',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            protein = 2,
            hunger = 3,
        },
    },
    
    ['butter'] = {
        label = 'Butter',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['condensedmilk'] = {
        label = 'Condensed Milk',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['yogurt'] = {
        label = 'Yogurt',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = {'food', 'drink'},
        nutrition = {
            dairy = 3,
            hunger = 3,
            thirst = 2,
        },
    },
    
    ['mozzarella'] = {
        label = 'Mozzarella Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
    },
    
    ['cubemozzarella'] = {
        label = 'Cubbed Mozzarella Cheese',
        weight = 50,    
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['stringmozzarella'] = {
        label = 'String Mozzarella Cheese',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['provolone'] = {
        label = 'Provolone Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
    },
    
    ['cubeprovolone'] = {
        label = 'Cubbed Provolone Cheese',
        weight = 50,    
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['stringprovolone'] = {
        label = 'String Provolone Cheese',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['cheddar'] = {
        label = 'Cheddar Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
    },
    
    ['cubecheddar'] = {
        label = 'Cubbed Cheddar Cheese',
        weight = 50,    
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['stringcheddar'] = {
        label = 'String Cheddar Cheese',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['bluecheese'] = {
        label = 'Blue Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['parmesan'] = {
        label = 'Parmesan Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
    },
    
    ['parmesanflakes'] = {
        label = 'Parmesan Flakes',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['ricotta'] = {
        label = 'Ricotta Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['creamcheese'] = {
        label = 'Cream Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['gouda'] = {
        label = 'Cream Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['cottagecheese'] = {
        label = 'Cottage Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['swiss'] = {
        label = 'Swiss Cheese',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = 'food',
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    ['icecream'] = {
        label = 'Ice Cream',
        weight = 50,   
        shopType = 'dairy',
        price = 10, 
        foodType = {'food', 'drink'},
        nutrition = {
            dairy = 3,
            hunger = 3,
        },
    },
    
    -- // Meat // --
    
    
    ['bologna'] = {
        label = 'Bologna',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
    },
    
    
    ['slicedbologna'] = {
        label = 'Sliced Bologna',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 5,
        },
    },
    
    ['wholeham'] = {
        label = 'Whole Ham',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
    },
    
    ['bacon'] = {
        label = 'Bacon Strips',
        weight = 50,   
        foodType = 'food',
        shopType = 'butcher',
        price = 10,
        nutrition = {
            protein = 3,
            hunger = 5,
        },
    },
    
    ['baconbits'] = {
        label = 'Bacon Bits',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 5,
        },
    },
    
    ['meatslab'] = {
        label = 'Slab of Meat',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
    },
    
    ['nystrip'] = {
        label = 'Raw NY Stip',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 8,
        },
    },
    
    ['filet'] = {
        label = 'Raw Beef Tenderloin',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 7,
        },
    },
    
    ['ribs'] = {
        label = 'Ribs',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 8,
        },
    },
    
    ['hotdog'] = {
        label = 'Hotdogs',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 4,
        },
    },
    
    ['roastbeef'] = {
        label = 'Roast Beef',
        weight = 50,   
        shopType = 'butcher',
        price = 10, 
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 6,
        },
    },
    
    ['slicedham'] = {
        label = 'Sliced Ham',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 6,
        },
    },
    
    ['dicedham'] = {
        label = 'Diced Ham',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 4,
        },
    },
    
    ['frozennuggets'] = {
        label = 'Frozen Nuggets',
        weight = 50,  
        shopType = 'butcher',
        price = 10,  
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 6,
        },
    },
    
    ['frozenchickenpatty'] = {
        label = 'Frozen Chicken Patty',
        weight = 50,  
        shopType = 'butcher',
        price = 10,  
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 5,
        },
    },
    
    ['frozenbeefpatty'] = {
        label = 'Beef Patty',
        weight = 50,  
        shopType = 'butcher',
        price = 10,   
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 6,
        },
    },
    
    ['pepperoni'] = {
        label = 'Pepperoni',
        weight = 50,  
        shopType = 'butcher',
        price = 10,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 4,
        },
    },
    
    ['packagedchicken'] = {
        label = 'Packaged Chicken',
        weight = 50,  
        shopType = 'butcher',
        price = 10,   
        foodType = 'food',
    },
    
    ['venison'] = {
        label = 'Hunting Meat',
        weight = 50,  
        foodType = 'food',
        nutrition = {
            protein = 6,
            hunger = 13,
        },
    },
    
    ['chickenstrips'] = {
        label = 'Chicken Strips',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 4,
        },
    },
    
    ['chickenwings'] = {
        label = 'Chicken Wings',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 4,
            hunger = 5,
        },
    },
    
    ['catfishfilet'] = {
        label = 'Catfish Filet',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 7,
        },
    },
    
    ['redfishfilet'] = {
        label = 'Redfish Filet',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 7,
        },
    },
    
    ['salomfilet'] = {
        label = 'Salmon Filet',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 6,
        },
    },
    
    ['tunafilet'] = {
        label = 'Tuna Filet',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 8,
        },
    },
    
    ['stripedbassfilet'] = {
        label = 'Stripped Bass Filet',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 5,
        },
    },
    
    ['rawsquid'] = {
        label = 'Raw Squid',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            protein = 3,
            hunger = 4,
        },
    },
    
    -- // Bread/Carbs  // -- 
    
    ['breadloaf'] = {
        label = 'Bread Loaf',
        weight = 50,   
        foodType = 'food',
        shopType = 'bakery',
        price = 10, 
    },
    
    ['flour'] = {
        label = 'Flour',
        weight = 50,   
        foodType = 'food',
        shopType = 'bakery',
        price = 10, 
    },
    
    ['hotdogbun'] = {
        label = 'Hot Dog Buns',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 3,
        },
    },
    
    ['burgerbuns'] = {
        label = 'Burger Buns',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 4,
        },
    },
    
    ['flatbread'] = {
        label = 'Flat Bread',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 3,
        },
    },
    
    ['bagel'] = {
        label = 'Bagel',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 3,
        },
    },
    
    ['pizzadough'] = {
        label = 'Yeast',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            carbs = 2,
            hunger = 6,
        }, 
    },
    
    ['sandwichbread'] = {
        label = 'Sandwich Bread',
        weight = 50,   
        foodType = 'food',
        nutrition = {
            carbs = 3,
            hunger = 2,
        }, 
    },
    
    ['fettuccine'] = {
        label = 'Fettuccine Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 5,
            hunger = 4,
        },
    },
    
    ['spaghetti'] = {
        label = 'Spaghetti Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 5,
            hunger = 4,
        },
    },
    
    ['tortellini'] = {
        label = 'Tortellini Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 5,
            hunger = 4,
        },
    },
    
    ['linguine'] = {
        label = 'Linguine Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 5,
            hunger = 4,
        },
    },
    
    ['lasagna'] = {
        label = 'Lasagna Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 5,
            hunger = 4,
        },
    },
    
    ['macaroni'] = {
        label = 'Macaroni Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 5,
        },
    },
    
    ['rigatoni'] = {
        label = 'Macaroni Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 5,
        },
    },
    
    ['ramen'] = {
        label = 'Ramen Noodles',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 3,
        },
    },
    
    ['rice'] = {
        label = 'Rice',
        weight = 50,  
        shopType = 'bakery',
        price = 10,   
        foodType = 'food',
        nutrition = {
            carbs = 4,
            hunger = 4,
        },
    },
    
    -- // General Market // --
    
    ['coffeebean'] = {
        label = 'Coffee Beans',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 4,
            thirst = 5,
            seasoning = 2,
        },
    },
    
    ['ketchup'] = {
        label = 'Ketchup',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 2,
            seasoning = 2,
        },
    },
    
    ['mustard'] = {
        label = 'Mustard',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 2,
            seasoning = 2,
        },
    },
    
    ['bbqsauce'] = {
        label = 'BBQ Sauce',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 2,
            seasoning = 4,
        },
    },
    
    ['mint'] = {
        label = 'Mint',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 2,
            seasoning = 3,
        },
    },
    
    ['sauce'] = {
        label = 'Generic Sauce',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 3,
            seasoning = 2,
        },
    },
    
    ['chips'] = {
        label = 'Potato Chips',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            hunger = 3,
        },
    },
    
    ['chocolatecandies'] = {
        label = 'Chocolate Candy',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 3,
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['chocolatesyrup'] = {
        label = 'Chocolate Syrup',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 3,
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['sprinkles'] = {
        label = 'Assorted Sprinkles',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 2,
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['candy'] = {
        label = 'Assorted Candies',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 2,
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['sugar'] = {
        label = 'Sugar',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 2,
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['brownsugar'] = {
        label = 'Brown Sugar',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            sugar = 2,
            seasoning = 2,
            hunger = 1,
        },
    },
    
    
    ['salt'] = {
        label = 'Salt',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['pepper'] = {
        label = 'Pepper',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['basil'] = {
        label = 'Basil',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['chilipowder'] = {
        label = 'Chili Powder',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 3,
            hunger = 1,
        },
    },
    
    ['cinnamon'] = {
        label = 'Cinnamon',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 3,
            hunger = 1,
        },
    },
    
    ['garlicpowder'] = {
        label = 'Garlic Powder',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 3,
            hunger = 1,
        },
    },
    
    ['lemonpeper'] = {
        label = 'Lemon Pepper',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 4,
            hunger = 1,
        },
    },
    
    ['nutmeg'] = {
        label = 'Nutmeg',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['onionpowder'] = {
        label = 'Onion Powder',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['oregano'] = {
        label = 'Oregano',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 3,
            hunger = 1,
        },
    },
    
    ['paprika'] = {
        label = 'Paprika',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    ['pepperflakes'] = {
        label = 'Red Pepper Flakes',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 3,
            hunger = 1,
        },
    },
    
    ['thyme'] = {
        label = 'Thyme',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 1,
            hunger = 1,
        },
    },
    
    ['curry'] = {
        label = 'Curry',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'food',
        nutrition = {
            seasoning = 2,
            hunger = 1,
        },
    },
    
    
    
    -- // Alchol // -- 
    
    ['gin'] = {
        label = 'Gin',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['vodka'] = {
        label = 'Vodka',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['whiskey'] = {
        label = 'Whiskey',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['cognac'] = {
        label = 'Cognac',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['rum'] = {
        label = 'Rum',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['olives'] = {
        label = 'Olives',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = {'food', 'drink'},
        nutrition = {
            alcohol = 5,
            thirst = 3,
            hunger = 2
        },
    },
    
    ['tonic'] = {
        label = 'Tonic',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            alcohol = 5,
            thirst = 3,
        },
    },
    
    ['carbonatedwater'] = {
        label = 'Cabonated Water',
        weight = 50,  
        shopType = 'general',
        price = 10,   
        foodType = 'drink',
        nutrition = {
            thirst = 7,
        },
    },
}