-- Valic Housing System Database Structure
-- Author: stepan_valic
-- Description: Database tables for housing system with QBCore/QBX integration

-- Main housing table
CREATE TABLE IF NOT EXISTS `valic_housing` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `house_id` int(11) NOT NULL,
    `owner_charid` varchar(50) NOT NULL,
    `password` varchar(12) NOT NULL,
    `purchase_price` int(11) NOT NULL,
    `purchase_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    `last_password_change` timestamp DEFAULT CURRENT_TIMESTAMP,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`),
    UNIQUE KEY `house_id` (`house_id`),
    KEY `owner_charid` (`owner_charid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Housing access keys table (for shared access)
CREATE TABLE IF NOT EXISTS `valic_housing_keys` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `house_id` int(11) NOT NULL,
    `charid` varchar(50) NOT NULL,
    `access_level` enum('owner','keyholder') DEFAULT 'keyholder',
    `granted_by` varchar(50) NOT NULL,
    `granted_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`),
    KEY `house_id` (`house_id`),
    KEY `charid` (`charid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tour codes table (temporary access for viewing)
CREATE TABLE IF NOT EXISTS `valic_housing_tours` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `house_id` int(11) NOT NULL,
    `tour_code` varchar(6) NOT NULL,
    `charid` varchar(50) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NOT NULL,
    `is_used` tinyint(1) DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `house_id` (`house_id`),
    KEY `tour_code` (`tour_code`),
    KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password change logs for security
CREATE TABLE IF NOT EXISTS `valic_housing_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `house_id` int(11) NOT NULL,
    `charid` varchar(50) NOT NULL,
    `action` enum('purchase','password_change','key_grant','key_revoke','tour_start','sale') NOT NULL,
    `details` text DEFAULT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `house_id` (`house_id`),
    KEY `charid` (`charid`),
    KEY `action` (`action`),
    KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cleanup expired tour codes (run this periodically)
-- DELETE FROM `valic_housing_tours` WHERE `expires_at` < NOW() AND `is_used` = 0;
