# Admin Commands - Valic Housing System

## Přehled
Tento dokument popisuje admin příkazy dostupné v Valic Housing systému.

## Oprávnění
Admin příkazy jsou dostupné pouze pro hráče s následují<PERSON><PERSON><PERSON> skupinami:
- `admin`
- `superadmin`

Oprávnění se konfigurují v `config/server_config.lua`:
```lua
ServerConfig.Admin = {
    groups = {
        "admin",
        "superadmin"
    },
    commands = true
}
```

## Dostupné příkazy

### /adminsellhouse [ID domu]
Umožňuje adminovi iniciovat prodej domu vlastněného hráčem.

**Použití:**
```
/adminsellhouse 1
```

**Proces:**
1. Admin zadá příkaz s ID domu
2. Systém zkontroluje:
   - Zda má admin oprávnění
   - Zda dům existuje
   - Zda je dům vlastněn nějakým hráčem
   - Zda je majitel online
3. Adminovi se zobrazí dialog s informacemi o domu a majiteli
4. Po potvrzení se majiteli zobrazí dialog s potvrzením prodeje
5. Majitel může prodej potvrdit nebo odmítnout

**Informace zobrazené adminovi:**
- Název domu
- ID domu
- Jméno majitele
- Původní cena
- Prodejní cena (30% původní ceny)
- Datum koupě

**Varování pro majitele:**
- Ztráta všech věcí uložených v domě
- Ztráta všech klíčů a přístupů
- Nevratnost akce

**Chybové hlášky:**
- "Nemáte oprávnění k použití tohoto příkazu" - hráč není admin
- "Použití: /adminsellhouse [ID domu]" - chybí parametr
- "Neplatné ID domu" - ID není číslo
- "Dům s ID X neexistuje" - dům není v konfiguraci
- "Dům s ID X není vlastněn žádným hráčem" - dům nemá majitele
- "Majitel není online" - majitel není připojen na server

## Logování
Všechny admin prodeje jsou zaznamenány:
- Do databáze (`valic_housing_logs`)
- Do Discord webhook (pokud je nakonfigurován)
- Do server konzole (pokud je debug zapnutý)

## Bezpečnost
- Pouze hráči s admin oprávněními mohou používat příkazy
- Všechny akce jsou logovány
- Majitel musí potvrdit prodej
- Systém kontroluje všechny vstupy

## Technické detaily
- Callback funkce: `valic_housing:adminConfirmSale`, `valic_housing:ownerConfirmSale`
- Client eventy: `valic_housing:showAdminSellDialog`, `valic_housing:showOwnerSellConfirmation`
- Databázové tabulky: `valic_housing`, `valic_housing_keys`, `valic_housing_logs`
