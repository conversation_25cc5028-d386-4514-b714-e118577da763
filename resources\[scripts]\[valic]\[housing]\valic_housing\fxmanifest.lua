fx_version 'cerulean'
game 'gta5'

author 'stepan_valic'
description 'Housing system for QBCore/QBox with ox_lib/ox_inventory/ox_doorlock integration'
version '1.0.0'

lua54 'yes'

shared_scripts {
    '@ox_lib/init.lua',
    'config/config.lua',
    'config/houses.lua'
}

server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'config/server_config.lua',
    'server/main.lua',
    'server/doorlock.lua',
    'server/payments.lua'
}

client_scripts {
    'client/main.lua',
    'client/ui.lua',
    'client/npc.lua'
}

files {
    'locales/*.json',
    'sql/*.sql'
}

dependencies {
    'qb-core',
    'ox_lib',
    'ox_inventory',
    'ox_doorlock',
    'oxmysql',
    'ox_target'
}