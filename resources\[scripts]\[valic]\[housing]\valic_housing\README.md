# Valic Housing System

Komplexní systém pro správu nemovitostí v QBCore/QBX Core s integrací ox_lib, ox_inventory, ox_doorlock a ox_target.

## Funkce

- 🏠 **Nákup nemovitostí** - Hr<PERSON><PERSON>i mohou kupovat domy za ingame peníze
- 🔑 **Spr<PERSON>va hesel** - Číselná hesla (4-12 číslic) pro dveře s automatickou inicializací
- 👥 **<PERSON><PERSON><PERSON><PERSON><PERSON>** - Vlastníci mohou dát klíče hráčům v okolí (10m) s vizuálními šipkami
- 🚪 **Prohlídky** - 10minutové prohlídky s dočasným kódem
- 💰 **Prodej** - Prodej za 30% původní ceny
- 📊 **Discord logy** - Kompletní logování všech akcí
- 🌍 **Lokalizace** - Podpora více jaz<PERSON> (zatím čeština)

## Instalace

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON>
Ujistěte se, že máte nainstalované:
- QBCore nebo QBX Core
- ox_lib
- ox_inventory
- ox_doorlock
- oxmysql
- ox_target

### 2. Databáze
Spusťte SQL skript pro vytvoření tabulek:
```sql
-- Spusťte obsah souboru sql/valic_housing.sql
```

### 3. Konfigurace ox_doorlock
Systém automaticky upravuje hesla v ox_doorlock. Ujistěte se, že máte nejnovější verzi ox_doorlock s podporou pro `ox_doorlock:refreshPasswords` event.

**Automatická inicializace hesel:**
- Při startu resource se automaticky nastaví náhodná hesla pro všechny nevlastněné domy
- Vlastněné domy si obnoví hesla z databáze
- Po ukončení prohlídky se hesla vrátí na původní (vlastněné) nebo náhodné (nevlastněné)
- Po prodeji domu se nastaví náhodné heslo

### 4. Konfigurace domů
Upravte `config/houses.lua` podle vašich potřeb:

```lua
Config.Houses = {
    {
        id = 1,
        name = "Michael House",
        price = 500000,
        npc = {
            coords = vec4(-847.84, 161.71, 66.37, 118.04),
            scenario = "WORLD_HUMAN_CLIPBOARD"
        },
        adminPanel = {
            coords = vec3(-798.08, 180.07, 72.85)
        },
        tourZone = {
            -- Definice zóny pomocí rohů (polygon)
            points = {
                vec3(-870.0, 140.0, 65.0), -- Severozápadní roh
                vec3(-800.0, 140.0, 65.0), -- Severovýchodní roh
                vec3(-800.0, 200.0, 65.0), -- Jihovýchodní roh
                vec3(-870.0, 200.0, 65.0)  -- Jihozápadní roh
            },
            minZ = 60.0, -- Minimální výška zóny
            maxZ = 80.0, -- Maximální výška zóny
            debugMode = true -- Zobrazit zónu v debug režimu
        },
        doorIds = {67, 68, 69, 70, 71, 72} -- ID dveří z ox_doorlock
    }
}
```

### 5. Discord Webhook
Upravte `config/server_config.lua` s vaším webhook URL:
```lua
ServerConfig.Discord = {
    webhook = "YOUR_WEBHOOK_URL",
    avatar = "YOUR_AVATAR_URL",
    enabled = true
}
```

## Použití

### Pro hráče

1. **Prohlídka nemovitosti**
   - Přijděte k NPC realitního makléře
   - Vyberte "Prohlédnout nemovitost"
   - Dostanete 5sekundovou notifikaci s 4místným kódem
   - Kód a zbývající čas se zobrazí v pravém spodním rohu
   - Zůstaňte v polygon zóně prohlídky (definované rohy v konfiguraci)
   - Při opuštění zóny dostanete 20sekundové varování
   - V debug režimu se zóna vizuálně zobrazí
   - **Exkluzivní prohlídky**: Pouze jeden hráč může prohlížet konkrétní nemovitost najednou
   - **Ukončení prohlídky**: Vraťte se k jakémukoliv NPC a vyberte "Ukončit aktivní prohlídku"

2. **Nákup nemovitosti**
   - Přijděte k NPC realitního makléře
   - Vyberte "Koupit nemovitost"
   - Zvolte způsob platby (hotovost/banka)
   - Nastavte heslo (4-12 číslic)

3. **Správa nemovitosti**
   - Přijděte k PC v nemovitosti (ox_target na adminPanel souřadnicích)
   - Můžete změnit heslo, dát klíče, prodat dům
   - **Automatická změna NPC**: Po koupi se realitní makléř změní na sekuriťáka (bez target interakce)
   - **Dynamické blips**: Blips se mění podle vlastnictví (dostupné/vlastněné)
   - **Inteligentní klíče**: Zobrazují se pouze hráči v okolí (10m) s šipkami nad hlavou

### Pro administrátory

1. **Nastavení dveří**
   - Použijte `/doorlock` pro vytvoření zámků
   - Poznamenejte si ID dveří
   - Přidejte ID do konfigurace domu

2. **Admin panel**
   - Nakonfigurujte `adminPanel.coords` v houses.lua
   - Vytvoří se neviditelná target zóna (bez NPC)
   - Přístup mají pouze admin skupiny
   - Debug režim zobrazí i admin target zónu

## Konfigurace

### Základní nastavení (config/config.lua)
```lua
Config.Debug = true -- Debug režim
Config.Locale = 'cs' -- Jazyk
Config.MinPasswordLength = 4 -- Min. délka hesla
Config.MaxPasswordLength = 12 -- Max. délka hesla
Config.TourDuration = 10 -- Délka prohlídky v minutách
```

### Bezpečnostní nastavení (config/server_config.lua)
```lua
ServerConfig.Security = {
    maxPasswordAttempts = 3,
    lockoutTime = 10,
    passwordChangeInterval = 5,
    tourExpiration = 10
}
```

## API

### Server Exports
```lua
-- Získat data domu
local houseData = exports.valic_housing:GetHouseData(houseId)

-- Získat domy hráče
local houses = exports.valic_housing:GetPlayerHouses(charid)

-- Zkontrolovat přístup
local hasAccess = exports.valic_housing:HasHouseAccess(charid, houseId)
```

### Client Exports
```lua
-- Začít prohlídku
exports.valic_housing:StartTour(houseId)

-- Koupit nemovitost
exports.valic_housing:PurchaseProperty(houseId, paymentMethod, password)

-- Získat lokalizovaný text
local text = exports.valic_housing:L('key.subkey', param1, param2)
```

## Události

### Server
- `valic_housing:startTour` - Začít prohlídku
- `valic_housing:purchaseProperty` - Koupit nemovitost
- `valic_housing:changePassword` - Změnit heslo
- `valic_housing:giveKey` / `valic_housing:revokeKey` - Správa klíčů

### Client
- `valic_housing:tourEnding` - Prohlídka končí za 2 minuty
- `valic_housing:tourEnded` - Prohlídka skončila
- `valic_housing:keyReceived` / `valic_housing:keyRevoked` - Správa klíčů

## Troubleshooting

### Časté problémy

1. **Hesla se neaktualizují**
   - Zkontrolujte, že ox_doorlock má nejnovější verzi
   - Ověřte správnost doorIds v konfiguraci

2. **NPC se nezobrazují**
   - Zkontrolujte koordináty v houses.lua
   - Ověřte, že ox_target je správně nainstalován

3. **Databázové chyby**
   - Spusťte SQL skript znovu
   - Zkontrolujte oprávnění databáze

### Debug režim
Zapněte `Config.Debug = true` pro detailní logy v konzoli.

#### Vizualizace tour zón
- Nastavte `debugMode = true` u konkrétního domu v konfiguraci
- Debug režim se zapne pro celý dům (tour zóna + admin target)
- Zóna se zobrazí jako polygon s ox_lib zones
- Při vstupu/výstupu ze zóny se zobrazí textUI
- Pomáhá při nastavování správných souřadnic rohů

#### Nastavení polygon zóny
1. Najděte rohy vašeho pozemku (4+ body)
2. Zapište souřadnice do `tourZone.points`
3. Nastavte `minZ` a `maxZ` pro výškové omezení
4. Zapněte `debugMode = true` pro testování
5. Po dokončení nastavení vypněte debug režim

## Podpora

Pro podporu a hlášení chyb kontaktujte autora: stepan_valic

## Licence

Tento systém je vytvořen pro soukromé použití. Redistribuce bez souhlasu autora je zakázána.
