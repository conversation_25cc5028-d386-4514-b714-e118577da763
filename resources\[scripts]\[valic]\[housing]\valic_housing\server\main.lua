-- Valic Housing System - Main Server Logic
-- Author: stepan_valic

local QBCore = nil
local QBX = nil
local Framework = nil

-- Framework Detection
CreateThread(function()
    if GetResourceState('qbx_core') == 'started' then
        QBX = exports.qbx_core
        Framework = 'qbx'
        print('^2[Valic Housing]^7 QBX Core detected')
    elseif GetResourceState('qb-core') == 'started' then
        QBCore = exports['qb-core']:GetCoreObject()
        Framework = 'qb'
        print('^2[Valic Housing]^7 QB Core detected')
    else
        print('^1[Valic Housing]^7 No supported framework detected!')
        return
    end
end)

-- Utility Functions (Global for use in other files)
function GetPlayer(source)
    if Framework == 'qbx' then
        return QBX:GetPlayer(source)
    elseif Framework == 'qb' then
        return QBCore.Functions.GetPlayer(source)
    end
    return nil
end

function GetCharacterId(player)
    if not player then return nil end
    if Framework == 'qbx' or Framework == 'qb' then
        return player.PlayerData.citizenid
    end
    return nil
end

function GetPlayerMoney(player, account)
    if not player then return 0 end
    if Framework == 'qbx' or Framework == 'qb' then
        return player.PlayerData.money[account] or 0
    end
    return 0
end

function RemovePlayerMoney(player, account, amount)
    if not player then return false end
    if Framework == 'qbx' then
        return player.Functions.RemoveMoney(account, amount, 'housing-purchase')
    elseif Framework == 'qb' then
        return player.Functions.RemoveMoney(account, amount, 'housing-purchase')
    end
    return false
end

function AddPlayerMoney(player, account, amount)
    if not player then return false end
    if Framework == 'qbx' then
        return player.Functions.AddMoney(account, amount, 'housing-sale')
    elseif Framework == 'qb' then
        return player.Functions.AddMoney(account, amount, 'housing-sale')
    end
    return false
end

-- Database Functions
local function InitializeDatabase()
    -- Create tables one by one since oxmysql doesn't support multiple statements
    local queries = {
        [[CREATE TABLE IF NOT EXISTS `valic_housing` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `owner_charid` varchar(50) NOT NULL,
            `password` varchar(12) NOT NULL,
            `purchase_price` int(11) NOT NULL,
            `purchase_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `last_password_change` timestamp DEFAULT CURRENT_TIMESTAMP,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            UNIQUE KEY `house_id` (`house_id`),
            KEY `owner_charid` (`owner_charid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_keys` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `access_level` enum('owner','keyholder') DEFAULT 'keyholder',
            `granted_by` varchar(50) NOT NULL,
            `granted_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `charid` (`charid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_tours` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `tour_code` varchar(6) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `expires_at` timestamp NOT NULL,
            `is_used` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `tour_code` (`tour_code`),
            KEY `expires_at` (`expires_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `action` enum('purchase','password_change','key_grant','key_revoke','tour_start','sale') NOT NULL,
            `details` text DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `charid` (`charid`),
            KEY `action` (`action`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]]
    }

    for i, query in ipairs(queries) do
        MySQL.query(query, {}, function(result)
            if Config.Debug then
                print(string.format('^2[Valic Housing]^7 Database table %d created/verified', i))
            end
        end)
    end

    print('^2[Valic Housing]^7 Database initialization completed')
end

-- Discord Webhook Function (Global)
function SendDiscordLog(title, description, color, fields)
    if not ServerConfig.Discord.enabled then return end

    local embed = {
        {
            title = title,
            description = description,
            color = color or ServerConfig.Discord.color,
            fields = fields or {},
            timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ"),
            footer = {
                text = "Valic Housing System",
                icon_url = ServerConfig.Discord.avatar
            }
        }
    }

    PerformHttpRequest(ServerConfig.Discord.webhook, function(err, text, headers) end, 'POST', json.encode({
        username = ServerConfig.Discord.name,
        avatar_url = ServerConfig.Discord.avatar,
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Generate random codes
function GenerateRandomCode(length)
    local chars = "0123456789"
    local code = ""
    math.randomseed(os.time() + GetGameTimer())
    for i = 1, length do
        local rand = math.random(#chars)
        code = code .. string.sub(chars, rand, rand)
    end
    return code
end

-- House Management Functions (Global)
function GetHouseData(houseId)
    local result = MySQL.query.await('SELECT * FROM valic_housing WHERE house_id = ? AND is_active = 1', {houseId})
    return result[1] or nil
end

function GetPlayerHouses(charid)
    local result = MySQL.query.await([[
        SELECT vh.*, vhk.access_level
        FROM valic_housing vh
        LEFT JOIN valic_housing_keys vhk ON vh.house_id = vhk.house_id AND vhk.charid = ?
        WHERE (vh.owner_charid = ? OR vhk.charid = ?) AND vh.is_active = 1 AND (vhk.is_active = 1 OR vhk.is_active IS NULL)
    ]], {charid, charid, charid})
    return result
end

function HasHouseAccess(charid, houseId, requiredLevel)
    requiredLevel = requiredLevel or 'keyholder'

    local result = MySQL.query.await([[
        SELECT vh.owner_charid, vhk.access_level, vhk.is_active
        FROM valic_housing vh
        LEFT JOIN valic_housing_keys vhk ON vh.house_id = vhk.house_id AND vhk.charid = ?
        WHERE vh.house_id = ? AND vh.is_active = 1
    ]], {charid, houseId})

    if not result[1] then
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 HasHouseAccess: No data found for charid %s, house %d', charid, houseId))
        end
        return false
    end

    local data = result[1]
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 HasHouseAccess: charid %s, house %d, owner: %s, access_level: %s, is_active: %s',
            charid, houseId, data.owner_charid, data.access_level or 'nil', data.is_active or 'nil'))
    end

    if data.owner_charid == charid then
        if Config.Debug then
            print(string.format('^2[Valic Housing Debug]^7 HasHouseAccess: Owner access granted for charid %s', charid))
        end
        return true
    end

    if data.access_level and data.is_active == 1 and requiredLevel == 'keyholder' then
        if Config.Debug then
            print(string.format('^2[Valic Housing Debug]^7 HasHouseAccess: Keyholder access granted for charid %s', charid))
        end
        return true
    end

    if Config.Debug then
        print(string.format('^1[Valic Housing Debug]^7 HasHouseAccess: Access denied for charid %s', charid))
    end
    return false
end

-- Check if player is admin
function IsPlayerAdmin(source)
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 IsPlayerAdmin called for source: %d', source))
        print(string.format('^3[Valic Housing Debug]^7 ServerConfig.Admin.commands: %s', tostring(ServerConfig.Admin.commands)))
        print(string.format('^3[Valic Housing Debug]^7 Framework: %s', tostring(Framework)))
    end

    if not ServerConfig.Admin.commands then
        if Config.Debug then
            print('^1[Valic Housing Debug]^7 Admin commands disabled')
        end
        return false
    end

    local player = GetPlayer(source)
    if not player then
        if Config.Debug then
            print('^1[Valic Housing Debug]^7 Player not found')
        end
        return false
    end

    -- Pro majitele serveru - pokud je source 1 (console) nebo má god mode
    if source == 1 then
        if Config.Debug then
            print('^2[Valic Housing Debug]^7 Console access granted')
        end
        return true
    end

    -- Kontrola ace permissions (nejjednodušší způsob)
    if IsPlayerAceAllowed(source, 'valic_housing.admin') then
        if Config.Debug then
            print('^2[Valic Housing Debug]^7 Player má ace permission: valic_housing.admin')
        end
        return true
    end

    -- Kontrola základních admin ace permissions
    if IsPlayerAceAllowed(source, 'command.god') or IsPlayerAceAllowed(source, 'command.admin') then
        if Config.Debug then
            print('^2[Valic Housing Debug]^7 Player má základní admin ace permission')
        end
        return true
    end

    -- QBX kontrola skupin
    if Framework == 'qbx' then
        local QBX = exports.qbx_core
        local playerData = QBX:GetPlayer(source)
        if playerData then
            local groups = playerData.PlayerData.groups or {}
            if Config.Debug then
                print(string.format('^3[Valic Housing Debug]^7 Player groups: %s', json.encode(groups)))
            end

            for _, group in pairs(ServerConfig.Admin.groups) do
                if Config.Debug then
                    print(string.format('^3[Valic Housing Debug]^7 Kontroluji QBX skupinu: %s', group))
                end
                if groups[group] then
                    if Config.Debug then
                        print(string.format('^2[Valic Housing Debug]^7 Player má QBX skupinu: %s', group))
                    end
                    return true
                end
            end

            -- Kontrola job-based admin
            local job = playerData.PlayerData.job
            if job and (job.name == 'admin' or job.name == 'superadmin') then
                if Config.Debug then
                    print(string.format('^2[Valic Housing Debug]^7 Player má admin job: %s', job.name))
                end
                return true
            end
        end
    elseif Framework == 'qb' then
        local QBCore = exports['qb-core']:GetCoreObject()
        for _, group in pairs(ServerConfig.Admin.groups) do
            if Config.Debug then
                print(string.format('^3[Valic Housing Debug]^7 Kontroluji QB permission: %s', group))
            end
            if QBCore.Functions.HasPermission(source, group) then
                if Config.Debug then
                    print(string.format('^2[Valic Housing Debug]^7 Player má QB permission: %s', group))
                end
                return true
            end
        end
    end

    -- Fallback pro majitele serveru - kontrola steam ID nebo license
    local identifiers = GetPlayerIdentifiers(source)
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 Player identifiers: %s', json.encode(identifiers)))
    end

    -- Kontrola ace skupin podle server.cfg
    for _, group in pairs(ServerConfig.Admin.groups) do
        local groupAce = string.format('group.%s', group)
        if IsPlayerAceAllowed(source, groupAce) then
            if Config.Debug then
                print(string.format('^2[Valic Housing Debug]^7 Player má ace skupinu: %s', groupAce))
            end
            return true
        end
    end

    if Config.Debug then
        print('^1[Valic Housing Debug]^7 Player is not admin')
    end
    return false
end

-- Initialize random door passwords for all houses
local function InitializeDoorPasswords()
    -- Clean up inactive house records first
    local inactiveRecords = MySQL.query.await('SELECT * FROM valic_housing WHERE is_active = 0')
    if inactiveRecords and #inactiveRecords > 0 then
        for _, record in pairs(inactiveRecords) do
            if Config.Debug then
                print(string.format('^1[Valic Housing Debug]^7 Found inactive house record: house_id %d, owner %s', record.house_id, record.owner_charid))
            end
        end
        -- Delete inactive records to prevent conflicts
        MySQL.query('DELETE FROM valic_housing WHERE is_active = 0')
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Cleaned up %d inactive house records', #inactiveRecords))
        end
    end

    for _, house in pairs(Config.Houses) do
        if house.doorIds then
            -- Check if house is owned
            local houseData = GetHouseData(house.id)

            if not houseData then
                -- House is not owned, set random password
                local randomPassword = GenerateRandomCode(8)
                TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, randomPassword)

                if Config.Debug then
                    print(string.format('^3[Valic Housing]^7 Set random password for unowned house %d (%s)', house.id, house.name))
                end
            else
                -- House is owned, use existing password from database
                TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, houseData.password)

                if Config.Debug then
                    print(string.format('^3[Valic Housing]^7 Restored password for owned house %d (%s)', house.id, house.name))
                end
            end
        end
    end

    print('^2[Valic Housing]^7 Door passwords initialized')
end

-- Initialize on resource start
CreateThread(function()
    Wait(1000) -- Wait for other resources to load
    InitializeDatabase()

    -- Wait a bit more for ox_doorlock to be ready
    Wait(2000)
    InitializeDoorPasswords()

    print('^2[Valic Housing]^7 All callbacks registered and system initialized')
end)

-- Cleanup expired tours every 30 minutes
CreateThread(function()
    while true do
        Wait(ServerConfig.Database.cleanupInterval * 60 * 1000)
        MySQL.query('DELETE FROM valic_housing_tours WHERE expires_at < NOW()')
        if Config.Debug then
            print('^3[Valic Housing]^7 Cleaned up expired tour codes')
        end
    end
end)

-- Tour Management
lib.callback.register('valic_housing:startTour', function(source, houseId)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Check if house exists and is available
    local houseData = GetHouseData(houseId)
    if houseData then
        return false, 'property_owned'
    end

    -- Check if player already has active tour (not used/expired)
    local playerActiveTour = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE charid = ? AND expires_at > NOW() AND is_used = 0', {charid})
    if playerActiveTour[1] then
        return false, 'tour_already_active'
    end

    -- Check if this house already has an active tour by ANY player
    local houseActiveTour = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE house_id = ? AND expires_at > NOW() AND is_used = 0', {houseId})
    if houseActiveTour[1] then
        return false, 'house_tour_active'
    end

    -- Generate tour code
    local tourCode = GenerateRandomCode(Config.TourCodeLength)
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.TourDuration * 60))

    -- Insert tour record
    MySQL.insert('INSERT INTO valic_housing_tours (house_id, tour_code, charid, expires_at) VALUES (?, ?, ?, ?)', {
        houseId, tourCode, charid, expiresAt
    })

    -- Update doorlock passwords
    local house = Config.Houses[houseId]
    if house and house.doorIds then
        TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, tourCode)
    end

    -- Log the tour start
    SendDiscordLog('🏠 Tour Started', string.format('Player %s started tour for house %d', charid, houseId), 3447003, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Tour Code', value = tourCode, inline = true},
        {name = 'Duration', value = Config.TourDuration .. ' minutes', inline = true}
    })

    return true, 'tour_started', {code = tourCode, expires = Config.TourDuration}
end)

-- Purchase Property
lib.callback.register('valic_housing:purchaseProperty', function(source, houseId, paymentMethod, password)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Validate house
    local house = Config.Houses[houseId]
    if not house then return false, 'invalid_house' end

    -- Check if already owned (active house)
    local houseData = GetHouseData(houseId)
    if houseData then return false, 'already_owned' end

    -- Check if house record exists (even if inactive)
    local existingRecord = MySQL.query.await('SELECT * FROM valic_housing WHERE house_id = ?', {houseId})
    local hasExistingRecord = existingRecord and #existingRecord > 0

    -- Validate password
    if not password or #password < Config.MinPasswordLength or #password > Config.MaxPasswordLength then
        return false, 'invalid_password'
    end

    if not string.match(password, '^%d+$') then
        return false, 'password_not_numeric'
    end

    -- Check payment
    local playerMoney = GetPlayerMoney(player, paymentMethod)
    if playerMoney < house.price then
        return false, 'insufficient_funds'
    end

    -- Process payment
    if not RemovePlayerMoney(player, paymentMethod, house.price) then
        return false, 'payment_failed'
    end

    -- Insert or update house record
    if hasExistingRecord then
        -- Update existing record
        MySQL.update('UPDATE valic_housing SET owner_charid = ?, password = ?, purchase_price = ?, purchase_date = NOW(), is_active = 1 WHERE house_id = ?', {
            charid, password, house.price, houseId
        })
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Updated existing house record for house %d', houseId))
        end
    else
        -- Insert new record
        MySQL.insert('INSERT INTO valic_housing (house_id, owner_charid, password, purchase_price) VALUES (?, ?, ?, ?)', {
            houseId, charid, password, house.price
        })
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Created new house record for house %d', houseId))
        end
    end

    -- Update doorlock passwords
    if house.doorIds then
        TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, password)
    end

    -- Log purchase
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'purchase', json.encode({price = house.price, payment_method = paymentMethod})
    })

    -- Discord log
    SendDiscordLog('🏠 Property Purchased', string.format('Player %s purchased house %d', charid, houseId), 65280, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Price', value = ServerConfig.Payment.currencySymbol .. house.price, inline = true},
        {name = 'Payment Method', value = paymentMethod, inline = true}
    })

    -- Inform all clients about property purchase
    TriggerClientEvent('valic_housing:propertyPurchased', -1, houseId)

    return true, 'purchase_success'
end)

-- Debug command for cleaning inactive house records
RegisterCommand('cleanhouses', function(source, args, rawCommand)
    if source ~= 0 then return end -- Only console

    local inactiveRecords = MySQL.query.await('SELECT * FROM valic_housing WHERE is_active = 0')
    if inactiveRecords and #inactiveRecords > 0 then
        print(string.format('^3[Valic Housing]^7 Found %d inactive house records:', #inactiveRecords))
        for _, record in pairs(inactiveRecords) do
            print(string.format('^3[Valic Housing]^7 - House ID: %d, Owner: %s, Purchase Date: %s', record.house_id, record.owner_charid, record.purchase_date))
        end
        MySQL.query('DELETE FROM valic_housing WHERE is_active = 0')
        print(string.format('^2[Valic Housing]^7 Cleaned up %d inactive house records', #inactiveRecords))
    else
        print('^2[Valic Housing]^7 No inactive house records found')
    end
end, true)

-- Debug command for testing nearby players
RegisterCommand('testnearby', function(source, args, rawCommand)
    local player = GetPlayer(source)
    if not player then return end

    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    print(string.format('^3[Debug Command]^7 Player %d coords: %s', source, playerCoords))

    if Framework == 'qbx' then
        local allPlayers = QBX:GetPlayersData()
        print(string.format('^3[Debug Command]^7 Total QBX players: %d', #allPlayers))
        for _, playerData in pairs(allPlayers) do
            local targetCoords = GetEntityCoords(GetPlayerPed(playerData.source))
            local distance = #(playerCoords - targetCoords)
            print(string.format('^3[Debug Command]^7 Player %s (%d): distance %.2f', GetPlayerName(playerData.source), playerData.source, distance))
        end
    elseif Framework == 'qb' then
        local allPlayers = QBCore.Functions.GetPlayers()
        print(string.format('^3[Debug Command]^7 Total QB players: %d', #allPlayers))
        for _, playerId in pairs(allPlayers) do
            local targetCoords = GetEntityCoords(GetPlayerPed(playerId))
            local distance = #(playerCoords - targetCoords)
            print(string.format('^3[Debug Command]^7 Player %s (%d): distance %.2f', GetPlayerName(playerId), playerId, distance))
        end
    end
end, false)

-- Get nearby players for key management
lib.callback.register('valic_housing:getNearbyPlayers', function(source, houseId)
    print('^3[Valic Housing Debug]^7 getNearbyPlayers callback called by source: ' .. source .. ' for house: ' .. (houseId or 'nil'))
    local player = GetPlayer(source)
    if not player then
        print('^1[Valic Housing Debug]^7 Player not found for source: ' .. source)
        return {}
    end

    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local nearbyPlayers = {}
    local maxDistance = 10.0 -- 10 meters radius

    print(string.format('^3[Valic Housing Debug]^7 Looking for players near %s at coords: %s', source, playerCoords))
    print(string.format('^3[Valic Housing Debug]^7 Framework detected: %s', Framework or 'nil'))

    if Framework == 'qbx' then
        print('^3[Valic Housing Debug]^7 Using QBX framework')
        local allPlayers = QBX:GetPlayersData()
        print(string.format('^3[Valic Housing Debug]^7 Total players on server: %d', #allPlayers))

        for _, playerData in pairs(allPlayers) do
            if playerData.source ~= source then -- Don't include the requesting player
                local targetCoords = GetEntityCoords(GetPlayerPed(playerData.source))
                local distance = #(playerCoords - targetCoords)

                print(string.format('^3[Valic Housing Debug]^7 Player %s (%d) distance: %.2f', GetPlayerName(playerData.source), playerData.source, distance))

                if distance <= maxDistance then
                    local nearbyPlayer = {
                        citizenid = playerData.citizenid,
                        name = GetPlayerName(playerData.source), -- Ingame name
                        characterName = playerData.charinfo.firstname .. ' ' .. playerData.charinfo.lastname,
                        source = playerData.source,
                        coords = targetCoords
                    }
                    table.insert(nearbyPlayers, nearbyPlayer)
                    print(string.format('^2[Valic Housing Debug]^7 Added nearby player: %s (%s)', nearbyPlayer.name, nearbyPlayer.characterName))
                end
            end
        end
    elseif Framework == 'qb' then
        print('^3[Valic Housing Debug]^7 Using QB framework')
        local allPlayers = QBCore.Functions.GetPlayers()
        print(string.format('^3[Valic Housing Debug]^7 Total players on server: %d', #allPlayers))

        for _, playerId in pairs(allPlayers) do
            if playerId ~= source then -- Don't include the requesting player
                local playerData = QBCore.Functions.GetPlayer(playerId)
                if playerData then
                    local targetCoords = GetEntityCoords(GetPlayerPed(playerId))
                    local distance = #(playerCoords - targetCoords)

                    print(string.format('^3[Valic Housing Debug]^7 Player %s (%d) distance: %.2f', GetPlayerName(playerId), playerId, distance))

                    if distance <= maxDistance then
                        local nearbyPlayer = {
                            citizenid = playerData.PlayerData.citizenid,
                            name = GetPlayerName(playerId), -- Ingame name
                            characterName = playerData.PlayerData.charinfo.firstname .. ' ' .. playerData.PlayerData.charinfo.lastname,
                            source = playerId,
                            coords = targetCoords
                        }
                        table.insert(nearbyPlayers, nearbyPlayer)
                        print(string.format('^2[Valic Housing Debug]^7 Added nearby player: %s (%s)', nearbyPlayer.name, nearbyPlayer.characterName))
                    end
                end
            end
        end
    else
        print('^1[Valic Housing Debug]^7 No framework detected! Framework value: ' .. tostring(Framework))
    end

    print(string.format('^3[Valic Housing Debug]^7 Found %d nearby players', #nearbyPlayers))
    return nearbyPlayers
end)

-- Admin House Sale Command
RegisterCommand('adminsellhouse', function(source, args, rawCommand)
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 Admin command called by source: %d', source))
        print(string.format('^3[Valic Housing Debug]^7 Args: %s', json.encode(args)))
    end

    if not IsPlayerAdmin(source) then
        if Config.Debug then
            print(string.format('^1[Valic Housing Debug]^7 Admin check failed for source: %d', source))
        end
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "Nemáte oprávnění k použití tohoto příkazu."}
        })
        return
    end

    if Config.Debug then
        print(string.format('^2[Valic Housing Debug]^7 Admin check passed for source: %d', source))
    end

    if not args[1] then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            multiline = true,
            args = {"System", "Použití: /adminsellhouse [ID domu]"}
        })
        return
    end

    local houseId = tonumber(args[1])
    if not houseId then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "Neplatné ID domu."}
        })
        return
    end

    -- Check if house exists in config
    local house = Config.Houses[houseId]
    if not house then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "Dům s ID " .. houseId .. " neexistuje."}
        })
        return
    end

    -- Get house data
    local houseData = GetHouseData(houseId)
    if not houseData then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "Dům s ID " .. houseId .. " není vlastněn žádným hráčem."}
        })
        return
    end

    -- Get owner info
    local ownerInfo = MySQL.query.await('SELECT charinfo FROM players WHERE citizenid = ?', {houseData.owner_charid})
    local ownerName = 'Neznámý'
    if ownerInfo and ownerInfo[1] then
        local charinfo = json.decode(ownerInfo[1].charinfo)
        ownerName = charinfo.firstname .. ' ' .. charinfo.lastname
    end

    -- Add owner name to house data
    houseData.owner_name = ownerName

    -- Show admin confirmation dialog
    TriggerClientEvent('valic_housing:showAdminSellDialog', source, houseId, houseData, house)
end, false)

-- Admin confirm house sale
lib.callback.register('valic_housing:adminConfirmSale', function(source, houseId)
    if not IsPlayerAdmin(source) then
        return false, 'permission_denied'
    end

    local houseData = GetHouseData(houseId)
    if not houseData then
        return false, 'invalid_house'
    end

    -- Get owner player info
    local ownerInfo = MySQL.query.await('SELECT charinfo FROM players WHERE citizenid = ?', {houseData.owner_charid})
    if not ownerInfo[1] then
        return false, 'owner_not_found'
    end

    local charinfo = json.decode(ownerInfo[1].charinfo)
    local ownerName = charinfo.firstname .. ' ' .. charinfo.lastname

    -- Check if owner is online
    local ownerPlayer = GetPlayerByCitizenId(houseData.owner_charid)
    if not ownerPlayer then
        return false, 'owner_offline'
    end

    -- Send confirmation request to owner with admin source
    TriggerClientEvent('valic_housing:showOwnerSellConfirmation', ownerPlayer.PlayerData.source, houseId, houseData, ownerName, source)

    return true, 'confirmation_sent', {ownerName = ownerName}
end)

-- Owner confirms sale
lib.callback.register('valic_housing:ownerConfirmSale', function(source, houseId, adminSource)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end

    -- Calculate sale price
    local salePrice = math.floor(houseData.purchase_price * ServerConfig.Payment.resalePercentage)

    -- Add money to player
    if not AddPlayerMoney(player, 'bank', salePrice) then
        return false, 'payment_failed'
    end

    -- Remove house ownership
    MySQL.update('UPDATE valic_housing SET is_active = 0 WHERE house_id = ?', {houseId})

    -- Remove all keys
    MySQL.update('UPDATE valic_housing_keys SET is_active = 0 WHERE house_id = ?', {houseId})

    -- Generate random password for doors (house is now unowned)
    local randomPassword = GenerateRandomCode(8)
    local house = Config.Houses[houseId]
    if house and house.doorIds then
        UpdateDoorPasswords(house.doorIds, randomPassword)

        if Config.Debug then
            print(string.format('^3[Valic Housing]^7 House %d sold by admin, doors secured with random password', houseId))
        end
    end

    -- Log sale
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'admin_sale', json.encode({sale_price = salePrice, original_price = houseData.purchase_price})
    })

    -- Discord log
    SendDiscordLog('🏠 Property Sold (Admin)', string.format('House %d sold by admin for %s', houseId, charid), 16776960, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Sale Price', value = ServerConfig.Payment.currencySymbol .. salePrice, inline = true},
        {name = 'Original Price', value = ServerConfig.Payment.currencySymbol .. houseData.purchase_price, inline = true}
    })

    -- Inform all clients about property sale
    TriggerClientEvent('valic_housing:propertySold', -1, houseId)

    -- Notify admin about successful sale
    if adminSource and GetPlayerPing(adminSource) > 0 then -- Check if admin is still online
        local house = Config.Houses[houseId]
        local houseName = house and house.name or 'Neznámý dům'

        -- Get owner info
        local ownerInfo = MySQL.query.await('SELECT charinfo FROM players WHERE citizenid = ?', {charid})
        local ownerName = 'Neznámý'
        if ownerInfo and ownerInfo[1] then
            local charinfo = json.decode(ownerInfo[1].charinfo)
            ownerName = charinfo.firstname .. ' ' .. charinfo.lastname
        end

        TriggerClientEvent('valic_housing:notifyAdminSuccess', adminSource, houseId, houseName, ownerName, salePrice)
    end

    return true, 'property_sold', {salePrice = salePrice}
end)

-- Owner rejects sale
lib.callback.register('valic_housing:ownerRejectSale', function(source, houseId, adminSource)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Check if player is owner
    local houseData = GetHouseData(houseId)
    if not houseData or houseData.owner_charid ~= charid then
        return false, 'not_owner'
    end

    -- Get house info
    local house = Config.Houses[houseId]
    local houseName = house and house.name or 'Neznámý dům'

    -- Get owner info
    local ownerInfo = MySQL.query.await('SELECT charinfo FROM players WHERE citizenid = ?', {houseData.owner_charid})
    local ownerName = 'Neznámý'
    if ownerInfo and ownerInfo[1] then
        local charinfo = json.decode(ownerInfo[1].charinfo)
        ownerName = charinfo.firstname .. ' ' .. charinfo.lastname
    end

    -- Notify admin about rejection
    if adminSource and GetPlayerPing(adminSource) > 0 then -- Check if admin is still online
        TriggerClientEvent('valic_housing:notifyAdminRejection', adminSource, houseId, houseName, ownerName)
    end

    -- Log rejection
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'admin_sale_rejected', json.encode({admin_source = adminSource, house_name = houseName})
    })

    if Config.Debug then
        print(string.format('^3[Valic Housing]^7 House %d sale rejected by owner %s', houseId, ownerName))
    end

    return true, 'sale_rejected'
end)

-- Export functions for other resources
exports('GetHouseData', GetHouseData)
exports('GetPlayerHouses', GetPlayerHouses)
exports('HasHouseAccess', HasHouseAccess)
exports('IsPlayerAdmin', IsPlayerAdmin)
