-- Valic Housing System - Main Server Logic
-- Author: stepan_valic

local QBCore = nil
local QBX = nil
local Framework = nil

-- Framework Detection
CreateThread(function()
    if GetResourceState('qbx_core') == 'started' then
        QBX = exports.qbx_core
        Framework = 'qbx'
        print('^2[Valic Housing]^7 QBX Core detected')
    elseif GetResourceState('qb-core') == 'started' then
        QBCore = exports['qb-core']:GetCoreObject()
        Framework = 'qb'
        print('^2[Valic Housing]^7 QB Core detected')
    else
        print('^1[Valic Housing]^7 No supported framework detected!')
        return
    end
end)

-- Utility Functions (Global for use in other files)
function GetPlayer(source)
    if Framework == 'qbx' then
        return QBX:GetPlayer(source)
    elseif Framework == 'qb' then
        return QBCore.Functions.GetPlayer(source)
    end
    return nil
end

function GetCharacterId(player)
    if not player then return nil end
    if Framework == 'qbx' or Framework == 'qb' then
        return player.PlayerData.citizenid
    end
    return nil
end

function GetPlayerMoney(player, account)
    if not player then return 0 end
    if Framework == 'qbx' or Framework == 'qb' then
        return player.PlayerData.money[account] or 0
    end
    return 0
end

function RemovePlayerMoney(player, account, amount)
    if not player then return false end
    if Framework == 'qbx' then
        return player.Functions.RemoveMoney(account, amount, 'housing-purchase')
    elseif Framework == 'qb' then
        return player.Functions.RemoveMoney(account, amount, 'housing-purchase')
    end
    return false
end

function AddPlayerMoney(player, account, amount)
    if not player then return false end
    if Framework == 'qbx' then
        return player.Functions.AddMoney(account, amount, 'housing-sale')
    elseif Framework == 'qb' then
        return player.Functions.AddMoney(account, amount, 'housing-sale')
    end
    return false
end

-- Database Functions
local function InitializeDatabase()
    -- Create tables one by one since oxmysql doesn't support multiple statements
    local queries = {
        [[CREATE TABLE IF NOT EXISTS `valic_housing` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `owner_charid` varchar(50) NOT NULL,
            `password` varchar(12) NOT NULL,
            `purchase_price` int(11) NOT NULL,
            `purchase_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `last_password_change` timestamp DEFAULT CURRENT_TIMESTAMP,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            UNIQUE KEY `house_id` (`house_id`),
            KEY `owner_charid` (`owner_charid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_keys` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `access_level` enum('owner','keyholder') DEFAULT 'keyholder',
            `granted_by` varchar(50) NOT NULL,
            `granted_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `charid` (`charid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_tours` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `tour_code` varchar(6) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `expires_at` timestamp NOT NULL,
            `is_used` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `tour_code` (`tour_code`),
            KEY `expires_at` (`expires_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]],

        [[CREATE TABLE IF NOT EXISTS `valic_housing_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `house_id` int(11) NOT NULL,
            `charid` varchar(50) NOT NULL,
            `action` enum('purchase','password_change','key_grant','key_revoke','tour_start','sale') NOT NULL,
            `details` text DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `house_id` (`house_id`),
            KEY `charid` (`charid`),
            KEY `action` (`action`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci]]
    }

    for i, query in ipairs(queries) do
        MySQL.query(query, {}, function(result)
            if Config.Debug then
                print(string.format('^2[Valic Housing]^7 Database table %d created/verified', i))
            end
        end)
    end

    print('^2[Valic Housing]^7 Database initialization completed')
end

-- Discord Webhook Function (Global)
function SendDiscordLog(title, description, color, fields)
    if not ServerConfig.Discord.enabled then return end

    local embed = {
        {
            title = title,
            description = description,
            color = color or ServerConfig.Discord.color,
            fields = fields or {},
            timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ"),
            footer = {
                text = "Valic Housing System",
                icon_url = ServerConfig.Discord.avatar
            }
        }
    }

    PerformHttpRequest(ServerConfig.Discord.webhook, function(err, text, headers) end, 'POST', json.encode({
        username = ServerConfig.Discord.name,
        avatar_url = ServerConfig.Discord.avatar,
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Generate random codes
function GenerateRandomCode(length)
    local chars = "0123456789"
    local code = ""
    math.randomseed(os.time() + GetGameTimer())
    for i = 1, length do
        local rand = math.random(#chars)
        code = code .. string.sub(chars, rand, rand)
    end
    return code
end

-- House Management Functions (Global)
function GetHouseData(houseId)
    local result = MySQL.query.await('SELECT * FROM valic_housing WHERE house_id = ? AND is_active = 1', {houseId})
    return result[1] or nil
end

function GetPlayerHouses(charid)
    local result = MySQL.query.await([[
        SELECT vh.*, vhk.access_level
        FROM valic_housing vh
        LEFT JOIN valic_housing_keys vhk ON vh.house_id = vhk.house_id AND vhk.charid = ?
        WHERE (vh.owner_charid = ? OR vhk.charid = ?) AND vh.is_active = 1 AND (vhk.is_active = 1 OR vhk.is_active IS NULL)
    ]], {charid, charid, charid})
    return result
end

function HasHouseAccess(charid, houseId, requiredLevel)
    requiredLevel = requiredLevel or 'keyholder'

    local result = MySQL.query.await([[
        SELECT vh.owner_charid, vhk.access_level, vhk.is_active
        FROM valic_housing vh
        LEFT JOIN valic_housing_keys vhk ON vh.house_id = vhk.house_id AND vhk.charid = ?
        WHERE vh.house_id = ? AND vh.is_active = 1
    ]], {charid, houseId})

    if not result[1] then
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 HasHouseAccess: No data found for charid %s, house %d', charid, houseId))
        end
        return false
    end

    local data = result[1]
    if Config.Debug then
        print(string.format('^3[Valic Housing Debug]^7 HasHouseAccess: charid %s, house %d, owner: %s, access_level: %s, is_active: %s',
            charid, houseId, data.owner_charid, data.access_level or 'nil', data.is_active or 'nil'))
    end

    if data.owner_charid == charid then
        if Config.Debug then
            print(string.format('^2[Valic Housing Debug]^7 HasHouseAccess: Owner access granted for charid %s', charid))
        end
        return true
    end

    if data.access_level and data.is_active == 1 and requiredLevel == 'keyholder' then
        if Config.Debug then
            print(string.format('^2[Valic Housing Debug]^7 HasHouseAccess: Keyholder access granted for charid %s', charid))
        end
        return true
    end

    if Config.Debug then
        print(string.format('^1[Valic Housing Debug]^7 HasHouseAccess: Access denied for charid %s', charid))
    end
    return false
end

-- Initialize random door passwords for all houses
local function InitializeDoorPasswords()
    -- Clean up inactive house records first
    local inactiveRecords = MySQL.query.await('SELECT * FROM valic_housing WHERE is_active = 0')
    if inactiveRecords and #inactiveRecords > 0 then
        for _, record in pairs(inactiveRecords) do
            if Config.Debug then
                print(string.format('^1[Valic Housing Debug]^7 Found inactive house record: house_id %d, owner %s', record.house_id, record.owner_charid))
            end
        end
        -- Delete inactive records to prevent conflicts
        MySQL.query('DELETE FROM valic_housing WHERE is_active = 0')
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Cleaned up %d inactive house records', #inactiveRecords))
        end
    end

    for _, house in pairs(Config.Houses) do
        if house.doorIds then
            -- Check if house is owned
            local houseData = GetHouseData(house.id)

            if not houseData then
                -- House is not owned, set random password
                local randomPassword = GenerateRandomCode(8)
                TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, randomPassword)

                if Config.Debug then
                    print(string.format('^3[Valic Housing]^7 Set random password for unowned house %d (%s)', house.id, house.name))
                end
            else
                -- House is owned, use existing password from database
                TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, houseData.password)

                if Config.Debug then
                    print(string.format('^3[Valic Housing]^7 Restored password for owned house %d (%s)', house.id, house.name))
                end
            end
        end
    end

    print('^2[Valic Housing]^7 Door passwords initialized')
end

-- Initialize on resource start
CreateThread(function()
    Wait(1000) -- Wait for other resources to load
    InitializeDatabase()

    -- Wait a bit more for ox_doorlock to be ready
    Wait(2000)
    InitializeDoorPasswords()

    print('^2[Valic Housing]^7 All callbacks registered and system initialized')
end)

-- Cleanup expired tours every 30 minutes
CreateThread(function()
    while true do
        Wait(ServerConfig.Database.cleanupInterval * 60 * 1000)
        MySQL.query('DELETE FROM valic_housing_tours WHERE expires_at < NOW()')
        if Config.Debug then
            print('^3[Valic Housing]^7 Cleaned up expired tour codes')
        end
    end
end)

-- Tour Management
lib.callback.register('valic_housing:startTour', function(source, houseId)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Check if house exists and is available
    local houseData = GetHouseData(houseId)
    if houseData then
        return false, 'property_owned'
    end

    -- Check if player already has active tour (not used/expired)
    local playerActiveTour = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE charid = ? AND expires_at > NOW() AND is_used = 0', {charid})
    if playerActiveTour[1] then
        return false, 'tour_already_active'
    end

    -- Check if this house already has an active tour by ANY player
    local houseActiveTour = MySQL.query.await('SELECT * FROM valic_housing_tours WHERE house_id = ? AND expires_at > NOW() AND is_used = 0', {houseId})
    if houseActiveTour[1] then
        return false, 'house_tour_active'
    end

    -- Generate tour code
    local tourCode = GenerateRandomCode(Config.TourCodeLength)
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.TourDuration * 60))

    -- Insert tour record
    MySQL.insert('INSERT INTO valic_housing_tours (house_id, tour_code, charid, expires_at) VALUES (?, ?, ?, ?)', {
        houseId, tourCode, charid, expiresAt
    })

    -- Update doorlock passwords
    local house = Config.Houses[houseId]
    if house and house.doorIds then
        TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, tourCode)
    end

    -- Log the tour start
    SendDiscordLog('🏠 Tour Started', string.format('Player %s started tour for house %d', charid, houseId), 3447003, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Tour Code', value = tourCode, inline = true},
        {name = 'Duration', value = Config.TourDuration .. ' minutes', inline = true}
    })

    return true, 'tour_started', {code = tourCode, expires = Config.TourDuration}
end)

-- Purchase Property
lib.callback.register('valic_housing:purchaseProperty', function(source, houseId, paymentMethod, password)
    local player = GetPlayer(source)
    if not player then return false, 'player_not_found' end

    local charid = GetCharacterId(player)
    if not charid then return false, 'invalid_character' end

    -- Validate house
    local house = Config.Houses[houseId]
    if not house then return false, 'invalid_house' end

    -- Check if already owned (active house)
    local houseData = GetHouseData(houseId)
    if houseData then return false, 'already_owned' end

    -- Check if house record exists (even if inactive)
    local existingRecord = MySQL.query.await('SELECT * FROM valic_housing WHERE house_id = ?', {houseId})
    local hasExistingRecord = existingRecord and #existingRecord > 0

    -- Validate password
    if not password or #password < Config.MinPasswordLength or #password > Config.MaxPasswordLength then
        return false, 'invalid_password'
    end

    if not string.match(password, '^%d+$') then
        return false, 'password_not_numeric'
    end

    -- Check payment
    local playerMoney = GetPlayerMoney(player, paymentMethod)
    if playerMoney < house.price then
        return false, 'insufficient_funds'
    end

    -- Process payment
    if not RemovePlayerMoney(player, paymentMethod, house.price) then
        return false, 'payment_failed'
    end

    -- Insert or update house record
    if hasExistingRecord then
        -- Update existing record
        MySQL.update('UPDATE valic_housing SET owner_charid = ?, password = ?, purchase_price = ?, purchase_date = NOW(), is_active = 1 WHERE house_id = ?', {
            charid, password, house.price, houseId
        })
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Updated existing house record for house %d', houseId))
        end
    else
        -- Insert new record
        MySQL.insert('INSERT INTO valic_housing (house_id, owner_charid, password, purchase_price) VALUES (?, ?, ?, ?)', {
            houseId, charid, password, house.price
        })
        if Config.Debug then
            print(string.format('^3[Valic Housing Debug]^7 Created new house record for house %d', houseId))
        end
    end

    -- Update doorlock passwords
    if house.doorIds then
        TriggerEvent('ox_doorlock:refreshPasswords', house.doorIds, password)
    end

    -- Log purchase
    MySQL.insert('INSERT INTO valic_housing_logs (house_id, charid, action, details) VALUES (?, ?, ?, ?)', {
        houseId, charid, 'purchase', json.encode({price = house.price, payment_method = paymentMethod})
    })

    -- Discord log
    SendDiscordLog('🏠 Property Purchased', string.format('Player %s purchased house %d', charid, houseId), 65280, {
        {name = 'House ID', value = tostring(houseId), inline = true},
        {name = 'Price', value = ServerConfig.Payment.currencySymbol .. house.price, inline = true},
        {name = 'Payment Method', value = paymentMethod, inline = true}
    })

    -- Inform all clients about property purchase
    TriggerClientEvent('valic_housing:propertyPurchased', -1, houseId)

    return true, 'purchase_success'
end)

-- Debug command for cleaning inactive house records
RegisterCommand('cleanhouses', function(source, args, rawCommand)
    if source ~= 0 then return end -- Only console

    local inactiveRecords = MySQL.query.await('SELECT * FROM valic_housing WHERE is_active = 0')
    if inactiveRecords and #inactiveRecords > 0 then
        print(string.format('^3[Valic Housing]^7 Found %d inactive house records:', #inactiveRecords))
        for _, record in pairs(inactiveRecords) do
            print(string.format('^3[Valic Housing]^7 - House ID: %d, Owner: %s, Purchase Date: %s', record.house_id, record.owner_charid, record.purchase_date))
        end
        MySQL.query('DELETE FROM valic_housing WHERE is_active = 0')
        print(string.format('^2[Valic Housing]^7 Cleaned up %d inactive house records', #inactiveRecords))
    else
        print('^2[Valic Housing]^7 No inactive house records found')
    end
end, true)

-- Debug command for testing nearby players
RegisterCommand('testnearby', function(source, args, rawCommand)
    local player = GetPlayer(source)
    if not player then return end

    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    print(string.format('^3[Debug Command]^7 Player %d coords: %s', source, playerCoords))

    if Framework == 'qbx' then
        local allPlayers = QBX:GetPlayersData()
        print(string.format('^3[Debug Command]^7 Total QBX players: %d', #allPlayers))
        for _, playerData in pairs(allPlayers) do
            local targetCoords = GetEntityCoords(GetPlayerPed(playerData.source))
            local distance = #(playerCoords - targetCoords)
            print(string.format('^3[Debug Command]^7 Player %s (%d): distance %.2f', GetPlayerName(playerData.source), playerData.source, distance))
        end
    elseif Framework == 'qb' then
        local allPlayers = QBCore.Functions.GetPlayers()
        print(string.format('^3[Debug Command]^7 Total QB players: %d', #allPlayers))
        for _, playerId in pairs(allPlayers) do
            local targetCoords = GetEntityCoords(GetPlayerPed(playerId))
            local distance = #(playerCoords - targetCoords)
            print(string.format('^3[Debug Command]^7 Player %s (%d): distance %.2f', GetPlayerName(playerId), playerId, distance))
        end
    end
end, false)

-- Get nearby players for key management
lib.callback.register('valic_housing:getNearbyPlayers', function(source, houseId)
    print('^3[Valic Housing Debug]^7 getNearbyPlayers callback called by source: ' .. source .. ' for house: ' .. (houseId or 'nil'))
    local player = GetPlayer(source)
    if not player then
        print('^1[Valic Housing Debug]^7 Player not found for source: ' .. source)
        return {}
    end

    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local nearbyPlayers = {}
    local maxDistance = 10.0 -- 10 meters radius

    print(string.format('^3[Valic Housing Debug]^7 Looking for players near %s at coords: %s', source, playerCoords))
    print(string.format('^3[Valic Housing Debug]^7 Framework detected: %s', Framework or 'nil'))

    if Framework == 'qbx' then
        print('^3[Valic Housing Debug]^7 Using QBX framework')
        local allPlayers = QBX:GetPlayersData()
        print(string.format('^3[Valic Housing Debug]^7 Total players on server: %d', #allPlayers))

        for _, playerData in pairs(allPlayers) do
            if playerData.source ~= source then -- Don't include the requesting player
                local targetCoords = GetEntityCoords(GetPlayerPed(playerData.source))
                local distance = #(playerCoords - targetCoords)

                print(string.format('^3[Valic Housing Debug]^7 Player %s (%d) distance: %.2f', GetPlayerName(playerData.source), playerData.source, distance))

                if distance <= maxDistance then
                    local nearbyPlayer = {
                        citizenid = playerData.citizenid,
                        name = GetPlayerName(playerData.source), -- Ingame name
                        characterName = playerData.charinfo.firstname .. ' ' .. playerData.charinfo.lastname,
                        source = playerData.source,
                        coords = targetCoords
                    }
                    table.insert(nearbyPlayers, nearbyPlayer)
                    print(string.format('^2[Valic Housing Debug]^7 Added nearby player: %s (%s)', nearbyPlayer.name, nearbyPlayer.characterName))
                end
            end
        end
    elseif Framework == 'qb' then
        print('^3[Valic Housing Debug]^7 Using QB framework')
        local allPlayers = QBCore.Functions.GetPlayers()
        print(string.format('^3[Valic Housing Debug]^7 Total players on server: %d', #allPlayers))

        for _, playerId in pairs(allPlayers) do
            if playerId ~= source then -- Don't include the requesting player
                local playerData = QBCore.Functions.GetPlayer(playerId)
                if playerData then
                    local targetCoords = GetEntityCoords(GetPlayerPed(playerId))
                    local distance = #(playerCoords - targetCoords)

                    print(string.format('^3[Valic Housing Debug]^7 Player %s (%d) distance: %.2f', GetPlayerName(playerId), playerId, distance))

                    if distance <= maxDistance then
                        local nearbyPlayer = {
                            citizenid = playerData.PlayerData.citizenid,
                            name = GetPlayerName(playerId), -- Ingame name
                            characterName = playerData.PlayerData.charinfo.firstname .. ' ' .. playerData.PlayerData.charinfo.lastname,
                            source = playerId,
                            coords = targetCoords
                        }
                        table.insert(nearbyPlayers, nearbyPlayer)
                        print(string.format('^2[Valic Housing Debug]^7 Added nearby player: %s (%s)', nearbyPlayer.name, nearbyPlayer.characterName))
                    end
                end
            end
        end
    else
        print('^1[Valic Housing Debug]^7 No framework detected! Framework value: ' .. tostring(Framework))
    end

    print(string.format('^3[Valic Housing Debug]^7 Found %d nearby players', #nearbyPlayers))
    return nearbyPlayers
end)

-- Export functions for other resources
exports('GetHouseData', GetHouseData)
exports('GetPlayerHouses', GetPlayerHouses)
exports('HasHouseAccess', HasHouseAccess)
